"""Logging utilities for RAPTOR Test system."""

import logging
import sys
from pathlib import Path
from typing import Optional

from config.settings import get_settings


def setup_logging(log_level: Optional[str] = None, 
                 log_file: Optional[str] = None) -> None:
    """Setup logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
    """
    settings = get_settings()
    
    # Use provided values or fall back to settings
    level = log_level or settings.log_level
    file_path = log_file or settings.log_file
    
    # Create logs directory if it doesn't exist
    if file_path:
        log_path = Path(file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    handlers.append(console_handler)
    
    # File handler (if specified)
    if file_path:
        file_handler = logging.FileHandler(file_path, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper()))
        file_formatter = logging.Formatter(settings.log_format)
        file_handler.setFormatter(file_formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        handlers=handlers,
        force=True
    )
    
    # Set specific logger levels
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(self.__class__.__module__ + '.' + self.__class__.__name__)


class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, name: str):
        """Initialize performance logger.
        
        Args:
            name: Logger name
        """
        self.logger = get_logger(f"performance.{name}")
        self.metrics = {}
    
    def log_metric(self, metric_name: str, value: float, unit: str = "") -> None:
        """Log a performance metric.
        
        Args:
            metric_name: Name of the metric
            value: Metric value
            unit: Unit of measurement
        """
        self.metrics[metric_name] = value
        unit_str = f" {unit}" if unit else ""
        self.logger.info(f"{metric_name}: {value:.4f}{unit_str}")
    
    def log_timing(self, operation: str, duration: float) -> None:
        """Log timing information.
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
        """
        self.log_metric(f"{operation}_duration", duration, "seconds")
    
    def log_count(self, item: str, count: int) -> None:
        """Log count information.
        
        Args:
            item: Name of the item being counted
            count: Count value
        """
        self.log_metric(f"{item}_count", count, "items")
    
    def get_metrics(self) -> dict:
        """Get all logged metrics.
        
        Returns:
            Dictionary of metrics
        """
        return self.metrics.copy()
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        self.metrics.clear()


# Initialize logging when module is imported
try:
    setup_logging()
except Exception as e:
    # Fallback to basic logging if setup fails
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logging.getLogger(__name__).warning(f"Failed to setup logging: {e}")


# Export commonly used loggers
main_logger = get_logger("raptor_test")
performance_logger = PerformanceLogger("main")
