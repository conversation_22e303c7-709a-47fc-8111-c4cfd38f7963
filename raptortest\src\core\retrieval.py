"""Retrieval functionality for RAPTOR system."""

from typing import List, Dict, Any, Optional, Tuple
import logging
import numpy as np
from collections import defaultdict

from config.settings import get_settings
from src.core.embeddings import EmbeddingManager
from src.models.tree_node import TreeNode, RaptorTree
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RetrievalManager:
    """Manages document retrieval from RAPTOR trees."""
    
    def __init__(self):
        """Initialize the retrieval manager."""
        self.settings = get_settings()
        self.embedding_manager = EmbeddingManager()
        
    def retrieve_from_tree(self, tree: RaptorTree, 
                          query: str, 
                          k: Optional[int] = None,
                          strategy: str = "tree_traversal") -> List[TreeNode]:
        """Retrieve relevant nodes from the tree.
        
        Args:
            tree: RAPTOR tree to search
            query: Search query
            k: Number of nodes to retrieve
            strategy: Retrieval strategy
            
        Returns:
            List of relevant nodes
        """
        if k is None:
            k = self.settings.retrieval_k
            
        logger.info(f"Retrieving {k} nodes using {strategy} strategy")
        
        try:
            if strategy == "tree_traversal":
                return self._tree_traversal_retrieval(tree, query, k)
            elif strategy == "flat_retrieval":
                return self._flat_retrieval(tree, query, k)
            elif strategy == "level_based":
                return self._level_based_retrieval(tree, query, k)
            elif strategy == "hybrid":
                return self._hybrid_retrieval(tree, query, k)
            else:
                raise ValueError(f"Unknown retrieval strategy: {strategy}")
                
        except Exception as e:
            logger.error(f"Retrieval failed: {e}")
            return []
    
    def _tree_traversal_retrieval(self, tree: RaptorTree, 
                                 query: str, 
                                 k: int) -> List[TreeNode]:
        """Retrieve nodes using tree traversal strategy.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            k: Number of nodes to retrieve
            
        Returns:
            List of relevant nodes
        """
        logger.debug("Performing tree traversal retrieval")
        
        # Generate query embedding
        query_embedding = self.embedding_manager.embed_query(query)
        
        # Start from root nodes
        candidates = []
        for root_id in tree.root_ids:
            root_node = tree.get_node(root_id)
            if root_node:
                candidates.extend(self._traverse_node(tree, root_node, query_embedding, k))
        
        # Sort by similarity and return top k
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [node for node, score in candidates[:k]]
    
    def _traverse_node(self, tree: RaptorTree, 
                      node: TreeNode, 
                      query_embedding: List[float], 
                      k: int) -> List[Tuple[TreeNode, float]]:
        """Recursively traverse a node and its children.
        
        Args:
            tree: RAPTOR tree
            node: Current node
            query_embedding: Query embedding vector
            k: Number of nodes to retrieve
            
        Returns:
            List of (node, similarity_score) tuples
        """
        candidates = []
        
        # Calculate similarity for current node
        if node.embedding:
            similarity = self.embedding_manager.calculate_similarity(
                query_embedding, node.embedding
            )
            
            # Add node if it meets threshold
            if similarity >= self.settings.similarity_threshold:
                candidates.append((node, similarity))
        
        # If this is a leaf node, return
        if node.is_leaf():
            return candidates
        
        # Traverse children if similarity is high enough or if we need more candidates
        if (node.embedding and 
            self.embedding_manager.calculate_similarity(query_embedding, node.embedding) > 0.5) or \
           len(candidates) < k:
            
            children = tree.get_children(node.id)
            for child in children:
                child_candidates = self._traverse_node(tree, child, query_embedding, k)
                candidates.extend(child_candidates)
        
        return candidates
    
    def _flat_retrieval(self, tree: RaptorTree, 
                       query: str, 
                       k: int) -> List[TreeNode]:
        """Retrieve nodes using flat similarity search.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            k: Number of nodes to retrieve
            
        Returns:
            List of relevant nodes
        """
        logger.debug("Performing flat retrieval")
        
        query_embedding = self.embedding_manager.embed_query(query)
        
        # Calculate similarity for all nodes with embeddings
        similarities = []
        for node in tree.nodes.values():
            if node.embedding:
                similarity = self.embedding_manager.calculate_similarity(
                    query_embedding, node.embedding
                )
                similarities.append((node, similarity))
        
        # Sort by similarity and return top k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return [node for node, score in similarities[:k]]
    
    def _level_based_retrieval(self, tree: RaptorTree, 
                              query: str, 
                              k: int) -> List[TreeNode]:
        """Retrieve nodes level by level.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            k: Number of nodes to retrieve
            
        Returns:
            List of relevant nodes
        """
        logger.debug("Performing level-based retrieval")
        
        query_embedding = self.embedding_manager.embed_query(query)
        all_candidates = []
        
        # Search each level
        for level in range(tree.max_level + 1):
            level_nodes = tree.get_nodes_at_level(level)
            level_candidates = []
            
            for node in level_nodes:
                if node.embedding:
                    similarity = self.embedding_manager.calculate_similarity(
                        query_embedding, node.embedding
                    )
                    if similarity >= self.settings.similarity_threshold:
                        level_candidates.append((node, similarity))
            
            # Sort level candidates
            level_candidates.sort(key=lambda x: x[1], reverse=True)
            all_candidates.extend(level_candidates)
        
        # Sort all candidates and return top k
        all_candidates.sort(key=lambda x: x[1], reverse=True)
        return [node for node, score in all_candidates[:k]]
    
    def _hybrid_retrieval(self, tree: RaptorTree, 
                         query: str, 
                         k: int) -> List[TreeNode]:
        """Retrieve nodes using hybrid strategy.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            k: Number of nodes to retrieve
            
        Returns:
            List of relevant nodes
        """
        logger.debug("Performing hybrid retrieval")
        
        # Combine tree traversal and flat retrieval
        tree_results = self._tree_traversal_retrieval(tree, query, k // 2)
        flat_results = self._flat_retrieval(tree, query, k // 2)
        
        # Merge and deduplicate
        seen_ids = set()
        combined_results = []
        
        for node in tree_results + flat_results:
            if node.id not in seen_ids:
                combined_results.append(node)
                seen_ids.add(node.id)
        
        return combined_results[:k]
    
    def retrieve_with_context(self, tree: RaptorTree, 
                             query: str, 
                             k: Optional[int] = None,
                             include_ancestors: bool = True,
                             include_descendants: bool = False) -> List[TreeNode]:
        """Retrieve nodes with their context.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            k: Number of nodes to retrieve
            include_ancestors: Whether to include ancestor nodes
            include_descendants: Whether to include descendant nodes
            
        Returns:
            List of relevant nodes with context
        """
        if k is None:
            k = self.settings.retrieval_k
            
        logger.info(f"Retrieving {k} nodes with context")
        
        # Get initial results
        initial_results = self.retrieve_from_tree(tree, query, k)
        
        # Expand with context
        context_nodes = set(initial_results)
        
        for node in initial_results:
            if include_ancestors:
                ancestors = tree.get_ancestors(node.id)
                context_nodes.update(ancestors)
            
            if include_descendants:
                descendants = tree.get_descendants(node.id)
                context_nodes.update(descendants)
        
        return list(context_nodes)
    
    def get_retrieval_explanation(self, tree: RaptorTree, 
                                 query: str, 
                                 retrieved_nodes: List[TreeNode]) -> Dict[str, Any]:
        """Get explanation for retrieval results.
        
        Args:
            tree: RAPTOR tree
            query: Search query
            retrieved_nodes: Retrieved nodes
            
        Returns:
            Explanation dictionary
        """
        query_embedding = self.embedding_manager.embed_query(query)
        
        explanations = []
        for node in retrieved_nodes:
            if node.embedding:
                similarity = self.embedding_manager.calculate_similarity(
                    query_embedding, node.embedding
                )
                
                explanation = {
                    "node_id": node.id,
                    "level": node.level,
                    "similarity_score": similarity,
                    "content_preview": node.content[:100] + "..." if len(node.content) > 100 else node.content,
                    "is_leaf": node.is_leaf(),
                    "cluster_id": node.cluster_id,
                    "document_id": node.document_id
                }
                explanations.append(explanation)
        
        return {
            "query": query,
            "total_retrieved": len(retrieved_nodes),
            "explanations": explanations,
            "avg_similarity": np.mean([exp["similarity_score"] for exp in explanations]) if explanations else 0
        }
    
    def evaluate_retrieval_quality(self, tree: RaptorTree, 
                                  queries: List[str], 
                                  ground_truth: Optional[Dict[str, List[str]]] = None) -> Dict[str, Any]:
        """Evaluate retrieval quality.
        
        Args:
            tree: RAPTOR tree
            queries: List of test queries
            ground_truth: Optional ground truth relevance judgments
            
        Returns:
            Evaluation metrics
        """
        logger.info(f"Evaluating retrieval quality on {len(queries)} queries")
        
        results = {
            "total_queries": len(queries),
            "avg_retrieved": 0,
            "avg_similarity": 0,
            "coverage_by_level": defaultdict(int)
        }
        
        total_retrieved = 0
        total_similarity = 0
        
        for query in queries:
            retrieved = self.retrieve_from_tree(tree, query)
            total_retrieved += len(retrieved)
            
            # Calculate average similarity
            query_embedding = self.embedding_manager.embed_query(query)
            similarities = []
            for node in retrieved:
                if node.embedding:
                    sim = self.embedding_manager.calculate_similarity(
                        query_embedding, node.embedding
                    )
                    similarities.append(sim)
            
            if similarities:
                total_similarity += np.mean(similarities)
            
            # Track level coverage
            for node in retrieved:
                results["coverage_by_level"][node.level] += 1
        
        results["avg_retrieved"] = total_retrieved / len(queries) if queries else 0
        results["avg_similarity"] = total_similarity / len(queries) if queries else 0
        
        return results
