#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版本的 sci-ag.py 测试脚本
"""

import asyncio
import os
from dotenv import load_dotenv
from langchain_deepseek import ChatDeepSeek
from langchain_core.messages import SystemMessage, HumanMessage

# 加载环境变量
load_dotenv()

async def simple_test():
    """简单的异步测试"""
    try:
        print("🚀 开始简单测试...")
        
        # 创建 ChatDeepSeek 实例
        llm = ChatDeepSeek(model="deepseek-chat", temperature=0.0)
        
        # 测试简单对话
        messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content="Hello! Can you tell me what 2+2 equals?")
        ]
        
        print("📤 发送消息到 DeepSeek...")
        response = await llm.ainvoke(messages)
        
        print(f"📥 收到响应: {response.content}")
        print("✅ 简单测试成功!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_core_api():
    """测试 CORE API 连接"""
    try:
        print("\n🔍 测试 CORE API 连接...")
        
        import urllib3
        
        api_key = os.getenv('CORE_API_KEY')
        api_url = os.getenv('CORE_API_URL', 'https://api.core.ac.uk/v3')
        
        if not api_key:
            print("❌ CORE_API_KEY 未设置")
            return False
        
        http = urllib3.PoolManager()
        response = http.request(
            'GET',
            f"{api_url}/search/outputs", 
            headers={"Authorization": f"Bearer {api_key}"}, 
            fields={"q": "machine learning", "limit": 1}
        )
        
        if 200 <= response.status < 300:
            print("✅ CORE API 连接成功!")
            return True
        else:
            print(f"❌ CORE API 响应错误: {response.status}")
            return False
            
    except Exception as e:
        print(f"❌ CORE API 测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 运行简化测试...")
    
    # 测试 DeepSeek
    deepseek_ok = await simple_test()
    
    # 测试 CORE API
    core_ok = await test_core_api()
    
    print(f"\n📊 测试结果:")
    print(f"DeepSeek: {'✅' if deepseek_ok else '❌'}")
    print(f"CORE API: {'✅' if core_ok else '❌'}")
    
    if deepseek_ok and core_ok:
        print("\n🎉 所有测试通过! sci-ag.py 应该可以正常运行。")
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
