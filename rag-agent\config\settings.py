"""Configuration settings for RAG Agent system."""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings."""
    
    # Ollama Configuration
    ollama_base_url: str = Field(
        default="http://localhost:11434",
        env="OLLAMA_BASE_URL",
        description="Ollama server base URL"
    )
    
    embedding_model: str = Field(
        default="nomic-embed-text",
        env="EMBEDDING_MODEL",
        description="Embedding model name"
    )
    
    chat_model: str = Field(
        default="gemma3:12b",
        env="CHAT_MODEL",
        description="Chat model name"
    )
    
    # Model Parameters
    temperature: float = Field(
        default=0.1,
        env="TEMPERATURE",
        description="Model temperature for generation"
    )
    
    max_tokens: int = Field(
        default=1000,
        env="MAX_TOKENS",
        description="Maximum tokens for generation"
    )
    
    top_p: float = Field(
        default=0.9,
        env="TOP_P",
        description="Top-p sampling parameter"
    )
    
    # Document Processing
    chunk_size: int = Field(
        default=500,
        env="CHUNK_SIZE",
        description="Document chunk size"
    )
    
    chunk_overlap: int = Field(
        default=50,
        env="CHUNK_OVERLAP",
        description="Document chunk overlap"
    )
    
    max_documents: int = Field(
        default=100,
        env="MAX_DOCUMENTS",
        description="Maximum number of documents to process"
    )
    
    # Retrieval Settings
    retrieval_k: int = Field(
        default=4,
        env="RETRIEVAL_K",
        description="Number of documents to retrieve"
    )
    
    similarity_threshold: float = Field(
        default=0.7,
        env="SIMILARITY_THRESHOLD",
        description="Similarity threshold for retrieval"
    )
    
    search_type: str = Field(
        default="similarity",
        env="SEARCH_TYPE",
        description="Search type for retrieval"
    )
    
    # Vector Store Configuration
    vectorstore_type: str = Field(
        default="chroma",
        env="VECTORSTORE_TYPE",
        description="Vector store type"
    )
    
    vectorstore_path: str = Field(
        default="./data/vectorstore",
        env="VECTORSTORE_PATH",
        description="Vector store path"
    )
    
    collection_name: str = Field(
        default="rag_documents",
        env="COLLECTION_NAME",
        description="Vector store collection name"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL",
        description="Logging level"
    )
    
    log_file: str = Field(
        default="./logs/rag_agent.log",
        env="LOG_FILE",
        description="Log file path"
    )
    
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT",
        description="Log format string"
    )
    
    # Performance Settings
    enable_caching: bool = Field(
        default=True,
        env="ENABLE_CACHING",
        description="Enable caching"
    )
    
    cache_ttl: int = Field(
        default=3600,
        env="CACHE_TTL",
        description="Cache TTL in seconds"
    )
    
    max_concurrent_requests: int = Field(
        default=10,
        env="MAX_CONCURRENT_REQUESTS",
        description="Maximum concurrent requests"
    )
    
    # Quality Control
    enable_relevance_check: bool = Field(
        default=True,
        env="ENABLE_RELEVANCE_CHECK",
        description="Enable relevance checking"
    )
    
    enable_hallucination_check: bool = Field(
        default=True,
        env="ENABLE_HALLUCINATION_CHECK",
        description="Enable hallucination checking"
    )
    
    enable_document_highlight: bool = Field(
        default=True,
        env="ENABLE_DOCUMENT_HIGHLIGHT",
        description="Enable document highlighting"
    )
    
    min_relevance_score: float = Field(
        default=0.6,
        env="MIN_RELEVANCE_SCORE",
        description="Minimum relevance score"
    )
    
    # Data Sources
    default_urls: List[str] = Field(
        default=[
            "https://www.deeplearning.ai/the-batch/how-agents-can-improve-llm-performance/?ref=dl-staging-website.ghost.io",
            "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-2-reflection/?ref=dl-staging-website.ghost.io",
            "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-3-tool-use/?ref=dl-staging-website.ghost.io",
            "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-4-planning/?ref=dl-staging-website.ghost.io",
            "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-5-multi-agent-collaboration/?ref=dl-staging-website.ghost.io"
        ],
        description="Default URLs to load documents from"
    )
    
    # API Configuration
    api_host: str = Field(
        default="0.0.0.0",
        env="API_HOST",
        description="API host"
    )
    
    api_port: int = Field(
        default=8000,
        env="API_PORT",
        description="API port"
    )
    
    api_workers: int = Field(
        default=1,
        env="API_WORKERS",
        description="Number of API workers"
    )
    
    # Development Settings
    debug: bool = Field(
        default=False,
        env="DEBUG",
        description="Enable debug mode"
    )
    
    enable_profiling: bool = Field(
        default=False,
        env="ENABLE_PROFILING",
        description="Enable profiling"
    )
    
    save_intermediate_results: bool = Field(
        default=False,
        env="SAVE_INTERMEDIATE_RESULTS",
        description="Save intermediate results"
    )
    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
