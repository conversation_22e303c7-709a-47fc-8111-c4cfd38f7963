"""Main RAPTOR service that orchestrates all components."""

import time
import json
from typing import List, Optional, Dict, Any
from pathlib import Path

from config.settings import get_settings
from config.prompts import PromptTemplates
from src.core.embeddings import EmbeddingManager
from src.core.clustering import ClusteringManager
from src.core.summarization import SummarizationManager
from src.core.tree_builder import TreeBuilder
from src.core.retrieval import RetrievalManager
from src.models.tree_node import TreeNode, RaptorTree
from src.utils.logger import get_logger, PerformanceLogger
from src.utils.helpers import timing_decorator, ensure_directory

logger = get_logger(__name__)
performance_logger = PerformanceLogger("raptor_service")


class RaptorService:
    """Main RAPTOR service that orchestrates all components."""
    
    def __init__(self):
        """Initialize the RAPTOR service."""
        self.settings = get_settings()
        
        # Initialize core components
        self.embedding_manager = EmbeddingManager()
        self.clustering_manager = ClusteringManager()
        self.summarization_manager = SummarizationManager()
        self.tree_builder = TreeBuilder()
        self.retrieval_manager = RetrievalManager()
        
        # Initialize prompt templates
        self.prompts = PromptTemplates()
        
        # Storage for trees
        self.trees: Dict[str, RaptorTree] = {}
        
        logger.info("RAPTOR service initialized successfully")
    
    def test_system_components(self) -> bool:
        """Test all system components.
        
        Returns:
            True if all tests pass, False otherwise
        """
        logger.info("🧪 Testing RAPTOR system components...")
        
        tests = [
            ("Embedding Model", self.embedding_manager.test_embedding_model),
            ("Summarization Model", self.summarization_manager.test_summarization),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"Testing {test_name}...")
            if not test_func():
                logger.error(f"❌ {test_name} test failed")
                return False
            logger.info(f"✅ {test_name} test passed")
        
        return True
    
    @timing_decorator
    def build_tree_from_documents(self, documents: List[str], 
                                 document_ids: Optional[List[str]] = None,
                                 tree_id: Optional[str] = None) -> RaptorTree:
        """Build a RAPTOR tree from documents.
        
        Args:
            documents: List of document texts
            document_ids: Optional list of document IDs
            tree_id: Optional tree identifier
            
        Returns:
            Constructed RAPTOR tree
        """
        logger.info(f"Building RAPTOR tree from {len(documents)} documents")
        start_time = time.time()
        
        try:
            # Build the tree
            tree = self.tree_builder.build_tree(documents, document_ids)
            
            # Optimize the tree
            tree = self.tree_builder.optimize_tree(tree)
            
            # Store the tree
            if tree_id is None:
                tree_id = f"tree_{int(time.time())}"
            self.trees[tree_id] = tree
            
            # Log performance metrics
            elapsed_time = time.time() - start_time
            performance_logger.log_timing("tree_construction", elapsed_time)
            performance_logger.log_count("tree_nodes", tree.total_nodes)
            performance_logger.log_count("tree_levels", tree.max_level + 1)
            
            logger.info(f"Tree construction completed in {elapsed_time:.2f} seconds")
            logger.info(f"Tree statistics: {tree.total_nodes} nodes, {tree.max_level + 1} levels")
            
            return tree
            
        except Exception as e:
            logger.error(f"Tree construction failed: {e}")
            raise
    
    @timing_decorator
    def query_tree(self, tree_id: str, 
                   query: str, 
                   k: Optional[int] = None,
                   strategy: str = "tree_traversal",
                   generate_answer: bool = True) -> Dict[str, Any]:
        """Query a RAPTOR tree.
        
        Args:
            tree_id: Tree identifier
            query: Search query
            k: Number of nodes to retrieve
            strategy: Retrieval strategy
            generate_answer: Whether to generate an answer
            
        Returns:
            Query results
        """
        logger.info(f"Querying tree {tree_id} with strategy {strategy}")
        start_time = time.time()
        
        try:
            # Get the tree
            if tree_id not in self.trees:
                raise ValueError(f"Tree {tree_id} not found")
            
            tree = self.trees[tree_id]
            
            # Retrieve relevant nodes
            retrieval_start = time.time()
            retrieved_nodes = self.retrieval_manager.retrieve_from_tree(
                tree, query, k, strategy
            )
            retrieval_time = time.time() - retrieval_start
            
            # Generate answer if requested
            answer = ""
            generation_time = 0
            if generate_answer and retrieved_nodes:
                generation_start = time.time()
                answer = self._generate_answer(retrieved_nodes, query)
                generation_time = time.time() - generation_start
            
            # Get retrieval explanation
            explanation = self.retrieval_manager.get_retrieval_explanation(
                tree, query, retrieved_nodes
            )
            
            # Calculate total time
            total_time = time.time() - start_time
            
            # Log performance metrics
            performance_logger.log_timing("query_total", total_time)
            performance_logger.log_timing("query_retrieval", retrieval_time)
            performance_logger.log_timing("query_generation", generation_time)
            performance_logger.log_count("nodes_retrieved", len(retrieved_nodes))
            
            return {
                "query": query,
                "answer": answer,
                "retrieved_nodes": [
                    {
                        "id": node.id,
                        "level": node.level,
                        "content": node.content,
                        "is_leaf": node.is_leaf(),
                        "cluster_id": node.cluster_id,
                        "document_id": node.document_id
                    }
                    for node in retrieved_nodes
                ],
                "explanation": explanation,
                "metrics": {
                    "total_time": total_time,
                    "retrieval_time": retrieval_time,
                    "generation_time": generation_time,
                    "nodes_retrieved": len(retrieved_nodes),
                    "strategy": strategy
                }
            }
            
        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise
    
    def _generate_answer(self, nodes: List[TreeNode], query: str) -> str:
        """Generate answer from retrieved nodes.
        
        Args:
            nodes: Retrieved nodes
            query: Original query
            
        Returns:
            Generated answer
        """
        logger.debug("Generating answer from retrieved nodes")
        
        try:
            # Combine node contents
            context_parts = []
            for i, node in enumerate(nodes, 1):
                context_parts.append(f"Source {i} (Level {node.level}):\n{node.content}")
            
            context = "\n\n".join(context_parts)
            
            # Generate answer using query answering prompt
            prompt = self.prompts.get_query_answering_prompt()
            messages = prompt.format_messages(context=context, question=query)
            
            # Use summarization manager's LLM
            response = self.summarization_manager.llm.invoke(messages)
            answer = response.content.strip()
            
            logger.debug(f"Generated answer: {answer[:100]}...")
            return answer
            
        except Exception as e:
            logger.error(f"Answer generation failed: {e}")
            return "I apologize, but I couldn't generate an answer based on the retrieved information."
    
    def save_tree(self, tree_id: str, file_path: str) -> bool:
        """Save a tree to file.
        
        Args:
            tree_id: Tree identifier
            file_path: Path to save the tree
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if tree_id not in self.trees:
                logger.error(f"Tree {tree_id} not found")
                return False
            
            tree = self.trees[tree_id]
            
            # Ensure directory exists
            ensure_directory(Path(file_path).parent)
            
            # Save tree
            tree.save_to_file(file_path)
            logger.info(f"Tree {tree_id} saved to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save tree: {e}")
            return False
    
    def load_tree(self, tree_id: str, file_path: str) -> bool:
        """Load a tree from file.
        
        Args:
            tree_id: Tree identifier
            file_path: Path to load the tree from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            tree = RaptorTree.load_from_file(file_path)
            self.trees[tree_id] = tree
            logger.info(f"Tree {tree_id} loaded from {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load tree: {e}")
            return False
    
    def get_tree_statistics(self, tree_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a tree.
        
        Args:
            tree_id: Tree identifier
            
        Returns:
            Tree statistics or None if tree not found
        """
        if tree_id not in self.trees:
            logger.error(f"Tree {tree_id} not found")
            return None
        
        tree = self.trees[tree_id]
        return self.tree_builder.get_tree_statistics(tree)
    
    def list_trees(self) -> List[Dict[str, Any]]:
        """List all loaded trees.
        
        Returns:
            List of tree information
        """
        tree_list = []
        for tree_id, tree in self.trees.items():
            tree_list.append({
                "id": tree_id,
                "total_nodes": tree.total_nodes,
                "total_leaves": tree.total_leaves,
                "max_level": tree.max_level,
                "created_at": tree.created_at.isoformat()
            })
        return tree_list
    
    def delete_tree(self, tree_id: str) -> bool:
        """Delete a tree from memory.
        
        Args:
            tree_id: Tree identifier
            
        Returns:
            True if successful, False otherwise
        """
        if tree_id in self.trees:
            del self.trees[tree_id]
            logger.info(f"Tree {tree_id} deleted")
            return True
        else:
            logger.warning(f"Tree {tree_id} not found")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status information.
        
        Returns:
            Dictionary containing system status
        """
        try:
            # Test components
            embedding_status = "OK" if self.embedding_manager.test_embedding_model() else "ERROR"
            summarization_status = "OK" if self.summarization_manager.test_summarization() else "ERROR"
            
            return {
                "embedding_model_status": embedding_status,
                "summarization_model_status": summarization_status,
                "loaded_trees": len(self.trees),
                "tree_list": self.list_trees(),
                "settings": {
                    "max_tree_levels": self.settings.max_tree_levels,
                    "clustering_method": self.settings.clustering_method,
                    "retrieval_k": self.settings.retrieval_k
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {"error": str(e)}
