"""Clustering functionality for RAPTOR system."""

from typing import List, Optional, Dict, Any, <PERSON>ple
import logging
import numpy as np
from sklearn.mixture import GaussianMixture
from sklearn.cluster import KMeans, AgglomerativeClustering
from sklearn.metrics import silhouette_score, calinski_harabasz_score

from config.settings import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ClusteringManager:
    """Manages document clustering for RAPTOR tree construction."""
    
    def __init__(self):
        """Initialize the clustering manager."""
        self.settings = get_settings()
        
    def perform_clustering(self, embeddings: np.ndarray, 
                         n_clusters: Optional[int] = None,
                         method: Optional[str] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Perform clustering on embeddings.
        
        Args:
            embeddings: Array of embedding vectors
            n_clusters: Number of clusters (auto-determined if None)
            method: Clustering method (uses settings default if None)
            
        Returns:
            Tuple of (cluster labels, clustering metadata)
        """
        if method is None:
            method = self.settings.clustering_method
            
        if n_clusters is None:
            n_clusters = self._determine_optimal_clusters(embeddings, method)
            
        logger.info(f"Performing {method} clustering with {n_clusters} clusters")
        
        try:
            if method.lower() == "gmm":
                labels, metadata = self._gmm_clustering(embeddings, n_clusters)
            elif method.lower() == "kmeans":
                labels, metadata = self._kmeans_clustering(embeddings, n_clusters)
            elif method.lower() == "hierarchical":
                labels, metadata = self._hierarchical_clustering(embeddings, n_clusters)
            else:
                raise ValueError(f"Unsupported clustering method: {method}")
            
            # Add quality metrics
            metadata.update(self._calculate_clustering_quality(embeddings, labels))
            
            logger.info(f"Clustering completed successfully with {len(np.unique(labels))} clusters")
            return labels, metadata
            
        except Exception as e:
            logger.error(f"Clustering failed: {e}")
            raise
    
    def _gmm_clustering(self, embeddings: np.ndarray, 
                       n_clusters: int) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Perform Gaussian Mixture Model clustering.
        
        Args:
            embeddings: Array of embedding vectors
            n_clusters: Number of clusters
            
        Returns:
            Tuple of (cluster labels, metadata)
        """
        gm = GaussianMixture(
            n_components=n_clusters,
            random_state=42,
            covariance_type='full'
        )
        
        labels = gm.fit_predict(embeddings)
        
        metadata = {
            "method": "gmm",
            "n_clusters": n_clusters,
            "aic": gm.aic(embeddings),
            "bic": gm.bic(embeddings),
            "log_likelihood": gm.score(embeddings),
            "converged": gm.converged_,
            "n_iter": gm.n_iter_
        }
        
        return labels, metadata
    
    def _kmeans_clustering(self, embeddings: np.ndarray, 
                          n_clusters: int) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Perform K-means clustering.
        
        Args:
            embeddings: Array of embedding vectors
            n_clusters: Number of clusters
            
        Returns:
            Tuple of (cluster labels, metadata)
        """
        kmeans = KMeans(
            n_clusters=n_clusters,
            random_state=42,
            n_init=10
        )
        
        labels = kmeans.fit_predict(embeddings)
        
        metadata = {
            "method": "kmeans",
            "n_clusters": n_clusters,
            "inertia": kmeans.inertia_,
            "n_iter": kmeans.n_iter_,
            "cluster_centers": kmeans.cluster_centers_.tolist()
        }
        
        return labels, metadata
    
    def _hierarchical_clustering(self, embeddings: np.ndarray, 
                                n_clusters: int) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Perform hierarchical clustering.
        
        Args:
            embeddings: Array of embedding vectors
            n_clusters: Number of clusters
            
        Returns:
            Tuple of (cluster labels, metadata)
        """
        clustering = AgglomerativeClustering(
            n_clusters=n_clusters,
            linkage='ward'
        )
        
        labels = clustering.fit_predict(embeddings)
        
        metadata = {
            "method": "hierarchical",
            "n_clusters": n_clusters,
            "linkage": "ward",
            "n_leaves": clustering.n_leaves_,
            "n_connected_components": clustering.n_connected_components_
        }
        
        return labels, metadata
    
    def _determine_optimal_clusters(self, embeddings: np.ndarray, 
                                  method: str) -> int:
        """Determine optimal number of clusters.
        
        Args:
            embeddings: Array of embedding vectors
            method: Clustering method
            
        Returns:
            Optimal number of clusters
        """
        n_samples = len(embeddings)
        min_clusters = max(2, self.settings.min_cluster_size)
        max_clusters = min(self.settings.max_clusters, n_samples // 2)
        
        if max_clusters <= min_clusters:
            return min_clusters
        
        logger.debug(f"Determining optimal clusters between {min_clusters} and {max_clusters}")
        
        best_score = -1
        best_n_clusters = min_clusters
        
        for n_clusters in range(min_clusters, max_clusters + 1):
            try:
                if method.lower() == "gmm":
                    gm = GaussianMixture(n_components=n_clusters, random_state=42)
                    labels = gm.fit_predict(embeddings)
                    # Use BIC for GMM
                    score = -gm.bic(embeddings)  # Negative because lower BIC is better
                elif method.lower() == "kmeans":
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                    labels = kmeans.fit_predict(embeddings)
                    score = silhouette_score(embeddings, labels)
                else:  # hierarchical
                    clustering = AgglomerativeClustering(n_clusters=n_clusters)
                    labels = clustering.fit_predict(embeddings)
                    score = silhouette_score(embeddings, labels)
                
                if score > best_score:
                    best_score = score
                    best_n_clusters = n_clusters
                    
            except Exception as e:
                logger.warning(f"Failed to evaluate {n_clusters} clusters: {e}")
                continue
        
        logger.info(f"Optimal number of clusters: {best_n_clusters} (score: {best_score:.4f})")
        return best_n_clusters
    
    def _calculate_clustering_quality(self, embeddings: np.ndarray, 
                                    labels: np.ndarray) -> Dict[str, float]:
        """Calculate clustering quality metrics.
        
        Args:
            embeddings: Array of embedding vectors
            labels: Cluster labels
            
        Returns:
            Dictionary of quality metrics
        """
        try:
            n_clusters = len(np.unique(labels))
            
            if n_clusters < 2:
                return {"silhouette_score": 0.0, "calinski_harabasz_score": 0.0}
            
            silhouette = silhouette_score(embeddings, labels)
            calinski_harabasz = calinski_harabasz_score(embeddings, labels)
            
            return {
                "silhouette_score": float(silhouette),
                "calinski_harabasz_score": float(calinski_harabasz),
                "n_unique_clusters": n_clusters
            }
            
        except Exception as e:
            logger.warning(f"Failed to calculate clustering quality: {e}")
            return {"silhouette_score": 0.0, "calinski_harabasz_score": 0.0}
    
    def get_cluster_statistics(self, embeddings: np.ndarray, 
                             labels: np.ndarray) -> Dict[str, Any]:
        """Get detailed statistics for each cluster.
        
        Args:
            embeddings: Array of embedding vectors
            labels: Cluster labels
            
        Returns:
            Dictionary of cluster statistics
        """
        stats = {}
        unique_labels = np.unique(labels)
        
        for label in unique_labels:
            cluster_mask = labels == label
            cluster_embeddings = embeddings[cluster_mask]
            
            stats[f"cluster_{label}"] = {
                "size": int(np.sum(cluster_mask)),
                "centroid": np.mean(cluster_embeddings, axis=0).tolist(),
                "std": np.std(cluster_embeddings, axis=0).tolist(),
                "intra_cluster_distance": float(np.mean(
                    np.linalg.norm(
                        cluster_embeddings - np.mean(cluster_embeddings, axis=0),
                        axis=1
                    )
                ))
            }
        
        return stats
    
    def validate_clustering(self, embeddings: np.ndarray, 
                          labels: np.ndarray) -> bool:
        """Validate clustering results.
        
        Args:
            embeddings: Array of embedding vectors
            labels: Cluster labels
            
        Returns:
            True if clustering is valid, False otherwise
        """
        try:
            # Check if we have valid labels
            if len(labels) != len(embeddings):
                logger.error("Mismatch between embeddings and labels length")
                return False
            
            # Check if we have at least one cluster
            unique_labels = np.unique(labels)
            if len(unique_labels) == 0:
                logger.error("No clusters found")
                return False
            
            # Check cluster sizes
            for label in unique_labels:
                cluster_size = np.sum(labels == label)
                if cluster_size < self.settings.min_cluster_size:
                    logger.warning(f"Cluster {label} has size {cluster_size} < minimum {self.settings.min_cluster_size}")
            
            logger.info("Clustering validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Clustering validation failed: {e}")
            return False
