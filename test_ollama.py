#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 Ollama 配置和连接
"""

import requests
import time
from typing import List, Dict

def check_ollama_service() -> bool:
    """检查 Ollama 服务是否运行"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama 服务正在运行")
            return True
        else:
            print(f"❌ Ollama 服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Ollama 服务 (http://localhost:11434)")
        print("   请确保 Ollama 已安装并正在运行")
        return False
    except Exception as e:
        print(f"❌ 检查 Ollama 服务时出错: {e}")
        return False

def list_available_models() -> List[Dict]:
    """列出可用的模型"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"📋 发现 {len(models)} 个已安装的模型:")
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                size_mb = size / (1024 * 1024) if size > 0 else 0
                print(f"   - {name} ({size_mb:.1f} MB)")
            return models
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取模型列表时出错: {e}")
        return []

def test_embedding_model() -> bool:
    """测试嵌入模型"""
    print("\n🔍 测试嵌入模型...")
    try:
        from langchain_ollama import OllamaEmbeddings
        
        embedding_model = OllamaEmbeddings(
            model="nomic-embed-text",
            base_url="http://localhost:11434"
        )
        
        # 测试嵌入
        test_text = "This is a test sentence for embedding."
        embeddings = embedding_model.embed_query(test_text)
        
        print(f"✅ 嵌入模型测试成功")
        print(f"   文本: '{test_text}'")
        print(f"   嵌入维度: {len(embeddings)}")
        print(f"   前5个值: {embeddings[:5]}")
        return True
        
    except ImportError:
        print("❌ langchain_ollama 未安装")
        print("   请运行: pip install langchain-ollama")
        return False
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        if "nomic-embed-text" in str(e):
            print("   请运行: ollama pull nomic-embed-text")
        return False

def test_chat_model() -> bool:
    """测试聊天模型"""
    print("\n💬 测试聊天模型...")
    try:
        from langchain_ollama import ChatOllama
        
        llm = ChatOllama(
            model="qwen3:8b",
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        # 测试聊天
        test_question = "What is 2+2? Please answer briefly."
        print(f"   问题: '{test_question}'")
        print("   正在生成回答...")
        
        start_time = time.time()
        response = llm.invoke(test_question)
        end_time = time.time()
        
        print(f"✅ 聊天模型测试成功")
        print(f"   回答: {response.content}")
        print(f"   响应时间: {end_time - start_time:.2f} 秒")
        return True
        
    except ImportError:
        print("❌ langchain_ollama 未安装")
        print("   请运行: pip install langchain-ollama")
        return False
    except Exception as e:
        print(f"❌ 聊天模型测试失败: {e}")
        if "llama3.2" in str(e):
            print("   请运行: ollama pull llama3.2")
        return False

def suggest_models_to_install(available_models: List[Dict]) -> None:
    """建议安装的模型"""
    model_names = [model.get('name', '').split(':')[0] for model in available_models]
    
    required_models = {
        'nomic-embed-text': '嵌入模型（必需）',
        'llama3.2': '聊天模型（推荐）',
    }
    
    optional_models = {
        'mistral': '快速聊天模型（可选）',
        'qwen2.5': '中文友好模型（可选）',
        'codellama': '代码生成模型（可选）'
    }
    
    print("\n📦 模型安装建议:")
    
    # 检查必需模型
    for model, desc in required_models.items():
        if not any(model in name for name in model_names):
            print(f"❗ 缺少 {desc}: ollama pull {model}")
        else:
            print(f"✅ 已安装 {desc}")
    
    # 检查可选模型
    print("\n可选模型:")
    for model, desc in optional_models.items():
        if not any(model in name for name in model_names):
            print(f"💡 {desc}: ollama pull {model}")
        else:
            print(f"✅ 已安装 {desc}")

def main():
    """主测试函数"""
    print("🧪 Ollama 配置测试")
    print("=" * 50)
    
    # 检查服务
    service_ok = check_ollama_service()
    if not service_ok:
        print("\n❌ Ollama 服务未运行，请先启动 Ollama")
        return
    
    # 列出模型
    available_models = list_available_models()
    
    # 建议模型
    suggest_models_to_install(available_models)
    
    # 测试嵌入模型
    embedding_ok = test_embedding_model()
    
    # 测试聊天模型
    chat_ok = test_chat_model()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"Ollama 服务: {'✅' if service_ok else '❌'}")
    print(f"嵌入模型: {'✅' if embedding_ok else '❌'}")
    print(f"聊天模型: {'✅' if chat_ok else '❌'}")
    
    if all([service_ok, embedding_ok, chat_ok]):
        print("\n🎉 所有测试通过！Ollama 配置正确，可以运行 rag-ag.py")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
