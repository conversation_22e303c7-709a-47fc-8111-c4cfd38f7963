# Highlight used docs
from typing import List
import re
import json
from langchain.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.exceptions import OutputParserException
from pydantic import BaseModel, Field
from langchain_ollama import ChatOllama
from relevance import docs_to_use
from GenResult import generation, format_docs
from ragagent import docs, question
# Data model
class HighlightDocuments(BaseModel):
    """Return the specific part of a document used for answering the question."""

    id: List[str] = Field(
        ...,
        description="List of id of docs used to answers the question"
    )

    title: List[str] = Field(
        ...,
        description="List of titles used to answers the question"
    )

    source: List[str] = Field(
        ...,
        description="List of sources used to answers the question"
    )

    segment: List[str] = Field(
        ...,
        description="List of direct segements from used documents that answers the question"
    )

# LLM
llm = ChatOllama(model="qwen3:8b", base_url="http://localhost:11434", temperature=0.1)

# Custom parser to handle Qwen model output
class CustomHighlightParser(PydanticOutputParser):
    def parse_result(self, result, *, partial: bool = False):
        """Override parse_result to handle <think> tags in Qwen output"""
        # Get the text from the result
        if hasattr(result[0], 'message'):
            text = result[0].message.content
        else:
            text = str(result[0])

        # Remove <think> tags and any content between them
        cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL).strip()

        # Try to extract JSON from the cleaned text
        json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            try:
                parsed_json = json.loads(json_str)
                return HighlightDocuments(**parsed_json)
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Attempted to parse: {json_str[:200]}...")

        # If no valid JSON found, try the original parser with cleaned text
        try:
            # Create a mock result with cleaned text
            from langchain_core.messages import AIMessage
            from langchain_core.outputs import ChatGeneration
            cleaned_result = [ChatGeneration(message=AIMessage(content=cleaned_text))]
            return super().parse_result(cleaned_result, partial=partial)
        except Exception as e:
            # If all else fails, return empty structure
            print(f"Warning: Could not parse output, returning empty structure. Error: {e}")
            print(f"Original text: {text[:200]}...")
            return HighlightDocuments(id=[], title=[], source=[], segment=[])

# parser
parser = CustomHighlightParser(pydantic_object=HighlightDocuments)

# Prompt
system = """You are an advanced assistant for document search and retrieval. You are provided with the following:
1. A question.
2. A generated answer based on the question.
3. A set of documents that were referenced in generating the answer.

Your task is to identify and extract the exact inline segments from the provided documents that directly correspond to the content used to
generate the given answer. The extracted segments must be verbatim snippets from the documents, ensuring a word-for-word match with the text
in the provided documents.

Ensure that:
- (Important) Each segment is an exact match to a part of the document and is fully contained within the document text.
- The relevance of each segment to the generated answer is clear and directly supports the answer provided.
- (Important) If you didn't used the specific document don't mention it.

IMPORTANT: You must respond ONLY with valid JSON format. Do not include any thinking process, explanations, or additional text outside the JSON structure.

Used documents: <docs>{documents}</docs> \n\n User question: <question>{question}</question> \n\n Generated answer: <answer>{generation}</answer>

<format_instruction>
{format_instructions}
</format_instruction>

Remember: Respond ONLY with valid JSON. No additional text or thinking process."""


prompt = PromptTemplate(
    template= system,
    input_variables=["documents", "question", "generation"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

# Chain
doc_lookup = prompt | llm | parser

# Run
print("\n" + "="*60)
print("🔍 Running Document Highlight Analysis...")
print("="*60)

try:
    lookup_response = doc_lookup.invoke({"documents":format_docs(docs_to_use), "question": question, "generation": generation})

    print("\n✅ Document Highlight Analysis Completed!")
    print(f"📊 Found {len(lookup_response.id)} relevant document segments")

    # Display results
    for i, (doc_id, title, source, segment) in enumerate(zip(
        lookup_response.id,
        lookup_response.title,
        lookup_response.source,
        lookup_response.segment
    )):
        print(f"\n📄 Document {i+1}:")
        print(f"   ID: {doc_id}")
        print(f"   Title: {title}")
        print(f"   Source: {source}")
        print(f"   Segment: {segment[:200]}{'...' if len(segment) > 200 else ''}")

except Exception as e:
    print(f"❌ Error during document highlight analysis: {e}")
    lookup_response = None