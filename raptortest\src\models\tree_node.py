"""Tree node models for RAPTOR system."""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
import json
import uuid


class TreeNode(BaseModel):
    """Represents a node in the RAPTOR tree."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique node ID")
    level: int = Field(..., description="Level in the tree (0 = leaf)")
    content: str = Field(..., description="Node content (original text or summary)")
    embedding: Optional[List[float]] = Field(None, description="Node embedding vector")
    parent_id: Optional[str] = Field(None, description="Parent node ID")
    children_ids: List[str] = Field(default_factory=list, description="Children node IDs")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    cluster_id: Optional[int] = Field(None, description="Cluster ID if this is a cluster node")
    document_id: Optional[str] = Field(None, description="Original document ID if this is a leaf")
    
    # Statistics
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    size: int = Field(default=1, description="Number of leaf nodes under this node")
    depth: int = Field(default=0, description="Depth from this node to deepest leaf")
    
    class Config:
        """Pydantic config."""
        arbitrary_types_allowed = True
    
    def is_leaf(self) -> bool:
        """Check if this is a leaf node."""
        return len(self.children_ids) == 0
    
    def is_root(self) -> bool:
        """Check if this is a root node."""
        return self.parent_id is None
    
    def add_child(self, child_id: str) -> None:
        """Add a child node ID."""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
    
    def remove_child(self, child_id: str) -> None:
        """Remove a child node ID."""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)
    
    def update_statistics(self, size: int, depth: int) -> None:
        """Update node statistics."""
        self.size = size
        self.depth = depth
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert node to dictionary."""
        return {
            "id": self.id,
            "level": self.level,
            "content": self.content,
            "embedding": self.embedding,
            "parent_id": self.parent_id,
            "children_ids": self.children_ids,
            "metadata": self.metadata,
            "cluster_id": self.cluster_id,
            "document_id": self.document_id,
            "created_at": self.created_at.isoformat(),
            "size": self.size,
            "depth": self.depth
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TreeNode":
        """Create node from dictionary."""
        if "created_at" in data and isinstance(data["created_at"], str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        return cls(**data)


class RaptorTree(BaseModel):
    """Represents the complete RAPTOR tree structure."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique tree ID")
    nodes: Dict[str, TreeNode] = Field(default_factory=dict, description="All nodes in the tree")
    root_ids: List[str] = Field(default_factory=list, description="Root node IDs")
    
    # Tree metadata
    max_level: int = Field(default=0, description="Maximum level in the tree")
    total_nodes: int = Field(default=0, description="Total number of nodes")
    total_leaves: int = Field(default=0, description="Total number of leaf nodes")
    
    # Construction metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Tree creation timestamp")
    construction_metadata: Dict[str, Any] = Field(default_factory=dict, description="Tree construction metadata")
    
    class Config:
        """Pydantic config."""
        arbitrary_types_allowed = True
    
    def add_node(self, node: TreeNode) -> None:
        """Add a node to the tree."""
        self.nodes[node.id] = node
        
        # Update tree statistics
        self.total_nodes = len(self.nodes)
        self.total_leaves = sum(1 for node in self.nodes.values() if node.is_leaf())
        self.max_level = max(node.level for node in self.nodes.values()) if self.nodes else 0
        
        # Update root IDs
        if node.is_root() and node.id not in self.root_ids:
            self.root_ids.append(node.id)
    
    def remove_node(self, node_id: str) -> None:
        """Remove a node from the tree."""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            
            # Remove from parent's children
            if node.parent_id and node.parent_id in self.nodes:
                self.nodes[node.parent_id].remove_child(node_id)
            
            # Remove from root IDs if applicable
            if node_id in self.root_ids:
                self.root_ids.remove(node_id)
            
            # Remove the node
            del self.nodes[node_id]
            
            # Update statistics
            self.total_nodes = len(self.nodes)
            self.total_leaves = sum(1 for node in self.nodes.values() if node.is_leaf())
            self.max_level = max(node.level for node in self.nodes.values()) if self.nodes else 0
    
    def get_node(self, node_id: str) -> Optional[TreeNode]:
        """Get a node by ID."""
        return self.nodes.get(node_id)
    
    def get_children(self, node_id: str) -> List[TreeNode]:
        """Get children of a node."""
        node = self.get_node(node_id)
        if not node:
            return []
        return [self.nodes[child_id] for child_id in node.children_ids if child_id in self.nodes]
    
    def get_parent(self, node_id: str) -> Optional[TreeNode]:
        """Get parent of a node."""
        node = self.get_node(node_id)
        if not node or not node.parent_id:
            return None
        return self.get_node(node.parent_id)
    
    def get_ancestors(self, node_id: str) -> List[TreeNode]:
        """Get all ancestors of a node."""
        ancestors = []
        current = self.get_parent(node_id)
        while current:
            ancestors.append(current)
            current = self.get_parent(current.id)
        return ancestors
    
    def get_descendants(self, node_id: str) -> List[TreeNode]:
        """Get all descendants of a node."""
        descendants = []
        children = self.get_children(node_id)
        for child in children:
            descendants.append(child)
            descendants.extend(self.get_descendants(child.id))
        return descendants
    
    def get_leaves(self, node_id: Optional[str] = None) -> List[TreeNode]:
        """Get all leaf nodes under a node (or all leaves if node_id is None)."""
        if node_id is None:
            return [node for node in self.nodes.values() if node.is_leaf()]
        else:
            descendants = self.get_descendants(node_id)
            return [node for node in descendants if node.is_leaf()]
    
    def get_nodes_at_level(self, level: int) -> List[TreeNode]:
        """Get all nodes at a specific level."""
        return [node for node in self.nodes.values() if node.level == level]
    
    def get_path_to_root(self, node_id: str) -> List[TreeNode]:
        """Get path from node to root."""
        path = []
        current = self.get_node(node_id)
        while current:
            path.append(current)
            current = self.get_parent(current.id)
        return path
    
    def validate_tree(self) -> Dict[str, Any]:
        """Validate tree structure and return validation results."""
        issues = []
        
        # Check for orphaned nodes
        for node in self.nodes.values():
            if not node.is_root() and node.parent_id not in self.nodes:
                issues.append(f"Node {node.id} has invalid parent {node.parent_id}")
        
        # Check for invalid children references
        for node in self.nodes.values():
            for child_id in node.children_ids:
                if child_id not in self.nodes:
                    issues.append(f"Node {node.id} has invalid child {child_id}")
        
        # Check for cycles
        visited = set()
        for root_id in self.root_ids:
            if self._has_cycle(root_id, visited, set()):
                issues.append(f"Cycle detected starting from root {root_id}")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "total_nodes": self.total_nodes,
            "total_leaves": self.total_leaves,
            "max_level": self.max_level,
            "num_roots": len(self.root_ids)
        }
    
    def _has_cycle(self, node_id: str, visited: set, path: set) -> bool:
        """Check for cycles in the tree."""
        if node_id in path:
            return True
        if node_id in visited:
            return False
        
        visited.add(node_id)
        path.add(node_id)
        
        node = self.get_node(node_id)
        if node:
            for child_id in node.children_ids:
                if self._has_cycle(child_id, visited, path):
                    return True
        
        path.remove(node_id)
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert tree to dictionary."""
        return {
            "id": self.id,
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "root_ids": self.root_ids,
            "max_level": self.max_level,
            "total_nodes": self.total_nodes,
            "total_leaves": self.total_leaves,
            "created_at": self.created_at.isoformat(),
            "construction_metadata": self.construction_metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "RaptorTree":
        """Create tree from dictionary."""
        # Convert nodes
        nodes = {}
        for node_id, node_data in data.get("nodes", {}).items():
            nodes[node_id] = TreeNode.from_dict(node_data)
        
        # Convert created_at
        if "created_at" in data and isinstance(data["created_at"], str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        
        # Create tree
        tree_data = data.copy()
        tree_data["nodes"] = nodes
        return cls(**tree_data)
    
    def save_to_file(self, file_path: str) -> None:
        """Save tree to JSON file."""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> "RaptorTree":
        """Load tree from JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return cls.from_dict(data)
