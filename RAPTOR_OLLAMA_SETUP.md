# RAPTOR with Ollama 设置指南

## 📋 概述

这个版本的 RAPTOR 使用 Ollama 作为本地大语言模型后端，无需 OpenAI API 密钥，完全本地化运行。

## 🛠️ 安装步骤

### 1. 安装 Ollama

#### Windows:
```bash
# 下载并安装 Ollama
# 访问 https://ollama.ai/download 下载 Windows 版本
```

#### macOS:
```bash
brew install ollama
```

#### Linux:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. 启动 Ollama 服务

```bash
ollama serve
```

### 3. 下载所需模型

```bash
# 下载嵌入模型 (必需)
ollama pull nomic-embed-text

# 下载聊天模型 (选择一个)
ollama pull gemma3:12b        # 推荐，平衡性能和质量
# 或者
ollama pull llama3.1:8b       # 更快，质量稍低
# 或者
ollama pull qwen2.5:7b        # 中文支持更好
```

### 4. 安装 Python 依赖

```bash
pip install langchain langchain-ollama langchain-community
pip install numpy pandas scikit-learn matplotlib
pip install faiss-cpu python-dotenv
pip install pypdf  # 如果需要处理PDF文件
```

### 5. 配置环境变量

```bash
# 复制环境变量模板
cp .env.raptor .env

# 编辑 .env 文件，根据需要修改配置
```

## 🚀 运行 RAPTOR

```bash
python raptor_test.py
```

## 📊 预期输出

```
🚀 RAPTOR with Ollama - Starting...
🧪 Testing Ollama connection...
Testing embedding model: nomic-embed-text
✅ Embedding model working. Dimension: 768
Testing chat model: gemma3:12b
✅ Chat model working. Response: OK, I understand and can respond to your message...

📚 Loading documents...
Loaded 6 documents

🌳 Building RAPTOR tree...
Built tree with 3 levels

🔍 Building vector store...
⚙️ Creating retriever...
❓ Processing query...

============================================================
📋 QUERY RESULTS
============================================================
Query: What is the greenhouse effect?
...
🎉 RAPTOR processing completed successfully!
```

## 🔧 配置选项

### 模型选择

| 模型 | 大小 | 速度 | 质量 | 中文支持 |
|------|------|------|------|----------|
| gemma3:12b | 12B | 中等 | 高 | 好 |
| llama3.1:8b | 8B | 快 | 中等 | 中等 |
| qwen2.5:7b | 7B | 快 | 中等 | 优秀 |

### 环境变量

- `OLLAMA_BASE_URL`: Ollama 服务器地址 (默认: http://localhost:11434)
- `EMBEDDING_MODEL`: 嵌入模型名称 (默认: nomic-embed-text)
- `CHAT_MODEL`: 聊天模型名称 (默认: gemma3:12b)

## 🐛 故障排除

### 1. 连接错误
```
❌ Ollama connection failed
```
**解决方案:**
- 确保 Ollama 服务正在运行: `ollama serve`
- 检查端口是否被占用
- 验证模型是否已下载: `ollama list`

### 2. 模型未找到
```
Error: model not found
```
**解决方案:**
```bash
ollama pull nomic-embed-text
ollama pull gemma3:12b
```

### 3. 内存不足
**解决方案:**
- 使用更小的模型 (如 llama3.1:8b)
- 减少文档数量
- 增加系统内存

## 📈 性能优化

1. **模型选择**: 根据硬件选择合适大小的模型
2. **批处理**: 大量文档时考虑分批处理
3. **缓存**: 重复查询会自动使用缓存
4. **并行处理**: 可以修改代码支持并行嵌入生成

## 🔄 与 OpenAI 版本的差异

| 特性 | OpenAI 版本 | Ollama 版本 |
|------|-------------|-------------|
| 成本 | 按使用付费 | 完全免费 |
| 隐私 | 数据上传到云端 | 完全本地处理 |
| 速度 | 取决于网络 | 取决于本地硬件 |
| 模型选择 | 固定模型 | 多种开源模型 |
| 离线使用 | 不支持 | 完全支持 |

## 📝 注意事项

1. **硬件要求**: 建议至少 8GB RAM，16GB 更佳
2. **首次运行**: 模型下载和初始化需要时间
3. **文档大小**: 大文档可能需要较长处理时间
4. **模型更新**: 定期更新模型以获得更好性能

## 🎯 下一步

- 尝试不同的模型组合
- 调整聚类参数
- 添加更多文档类型支持
- 实现批量文档处理
