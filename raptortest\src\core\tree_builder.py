"""Tree building functionality for RAPTOR system."""

from typing import List, Dict, Any, Optional, Tuple
import logging
import numpy as np
from collections import defaultdict

from config.settings import get_settings
from src.core.embeddings import EmbeddingManager
from src.core.clustering import ClusteringManager
from src.core.summarization import SummarizationManager
from src.models.tree_node import TreeNode, RaptorTree
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TreeBuilder:
    """Builds RAPTOR trees from documents."""
    
    def __init__(self):
        """Initialize the tree builder."""
        self.settings = get_settings()
        self.embedding_manager = EmbeddingManager()
        self.clustering_manager = ClusteringManager()
        self.summarization_manager = SummarizationManager()
        
    def build_tree(self, documents: List[str], 
                   document_ids: Optional[List[str]] = None) -> RaptorTree:
        """Build a RAPTOR tree from documents.
        
        Args:
            documents: List of document texts
            document_ids: Optional list of document IDs
            
        Returns:
            Constructed RAPTOR tree
        """
        logger.info(f"Building RAPTOR tree from {len(documents)} documents")
        
        if document_ids is None:
            document_ids = [f"doc_{i}" for i in range(len(documents))]
        
        # Initialize tree
        tree = RaptorTree()
        
        # Create leaf nodes
        leaf_nodes = []
        for i, (doc, doc_id) in enumerate(zip(documents, document_ids)):
            node = TreeNode(
                level=0,
                content=doc,
                document_id=doc_id,
                metadata={"original_index": i}
            )
            leaf_nodes.append(node)
            tree.add_node(node)
        
        # Build tree levels recursively
        current_level_nodes = leaf_nodes
        level = 0
        
        while len(current_level_nodes) > 1 and level < self.settings.max_tree_levels:
            logger.info(f"Building level {level + 1} with {len(current_level_nodes)} nodes")
            
            next_level_nodes = self._build_level(
                current_level_nodes, 
                level + 1, 
                tree
            )
            
            if not next_level_nodes:
                logger.warning(f"No nodes created at level {level + 1}, stopping")
                break
            
            current_level_nodes = next_level_nodes
            level += 1
        
        # Update tree metadata
        tree.construction_metadata = {
            "total_levels": level + 1,
            "clustering_method": self.settings.clustering_method,
            "max_clusters": self.settings.max_clusters,
            "min_cluster_size": self.settings.min_cluster_size
        }
        
        logger.info(f"Tree construction completed with {level + 1} levels")
        return tree
    
    def _build_level(self, nodes: List[TreeNode], 
                    level: int, 
                    tree: RaptorTree) -> List[TreeNode]:
        """Build a single level of the tree.
        
        Args:
            nodes: Nodes from the previous level
            level: Current level being built
            tree: Tree being constructed
            
        Returns:
            List of nodes created at this level
        """
        if len(nodes) <= self.settings.min_cluster_size:
            logger.info(f"Too few nodes ({len(nodes)}) for clustering at level {level}")
            return []
        
        # Generate embeddings for nodes
        node_texts = [node.content for node in nodes]
        embeddings = self.embedding_manager.embed_documents(node_texts)
        embeddings_array = np.array(embeddings)
        
        # Store embeddings in nodes
        for node, embedding in zip(nodes, embeddings):
            node.embedding = embedding
        
        # Perform clustering
        labels, clustering_metadata = self.clustering_manager.perform_clustering(
            embeddings_array
        )
        
        # Group nodes by cluster
        clusters = defaultdict(list)
        for node, label in zip(nodes, labels):
            clusters[label].append(node)
        
        # Create parent nodes for each cluster
        parent_nodes = []
        for cluster_id, cluster_nodes in clusters.items():
            if len(cluster_nodes) < self.settings.min_cluster_size:
                logger.debug(f"Skipping small cluster {cluster_id} with {len(cluster_nodes)} nodes")
                continue
            
            # Create cluster summary
            cluster_texts = [node.content for node in cluster_nodes]
            summary = self.summarization_manager.summarize_cluster(
                cluster_texts, 
                cluster_id, 
                level
            )
            
            # Create parent node
            parent_node = TreeNode(
                level=level,
                content=summary,
                cluster_id=cluster_id,
                metadata={
                    "cluster_size": len(cluster_nodes),
                    "clustering_metadata": clustering_metadata
                }
            )
            
            # Set parent-child relationships
            for child_node in cluster_nodes:
                child_node.parent_id = parent_node.id
                parent_node.add_child(child_node.id)
            
            parent_nodes.append(parent_node)
            tree.add_node(parent_node)
        
        logger.info(f"Created {len(parent_nodes)} parent nodes at level {level}")
        return parent_nodes
    
    def rebuild_tree_level(self, tree: RaptorTree, 
                          level: int) -> bool:
        """Rebuild a specific level of the tree.
        
        Args:
            tree: Tree to modify
            level: Level to rebuild
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Rebuilding tree level {level}")
        
        try:
            # Get nodes at the specified level
            level_nodes = tree.get_nodes_at_level(level)
            if not level_nodes:
                logger.warning(f"No nodes found at level {level}")
                return False
            
            # Get child nodes (level - 1)
            child_nodes = []
            for node in level_nodes:
                children = tree.get_children(node.id)
                child_nodes.extend(children)
            
            # Remove current level nodes
            for node in level_nodes:
                tree.remove_node(node.id)
            
            # Rebuild the level
            new_nodes = self._build_level(child_nodes, level, tree)
            
            logger.info(f"Successfully rebuilt level {level} with {len(new_nodes)} nodes")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rebuild tree level {level}: {e}")
            return False
    
    def optimize_tree(self, tree: RaptorTree) -> RaptorTree:
        """Optimize tree structure for better retrieval.
        
        Args:
            tree: Tree to optimize
            
        Returns:
            Optimized tree
        """
        logger.info("Optimizing tree structure")
        
        try:
            # Validate tree structure
            validation = tree.validate_tree()
            if not validation["is_valid"]:
                logger.warning(f"Tree validation issues: {validation['issues']}")
            
            # Update node statistics
            self._update_node_statistics(tree)
            
            # Prune small clusters if needed
            self._prune_small_clusters(tree)
            
            logger.info("Tree optimization completed")
            return tree
            
        except Exception as e:
            logger.error(f"Tree optimization failed: {e}")
            return tree
    
    def _update_node_statistics(self, tree: RaptorTree) -> None:
        """Update statistics for all nodes in the tree.
        
        Args:
            tree: Tree to update
        """
        for node in tree.nodes.values():
            if node.is_leaf():
                node.update_statistics(size=1, depth=0)
            else:
                descendants = tree.get_descendants(node.id)
                leaves = [d for d in descendants if d.is_leaf()]
                max_depth = max([d.level for d in descendants]) - node.level if descendants else 0
                node.update_statistics(size=len(leaves), depth=max_depth)
    
    def _prune_small_clusters(self, tree: RaptorTree) -> None:
        """Prune clusters that are too small.
        
        Args:
            tree: Tree to prune
        """
        nodes_to_remove = []
        
        for node in tree.nodes.values():
            if not node.is_leaf() and node.size < self.settings.min_cluster_size:
                logger.debug(f"Marking small cluster node {node.id} for removal")
                nodes_to_remove.append(node.id)
        
        for node_id in nodes_to_remove:
            tree.remove_node(node_id)
        
        if nodes_to_remove:
            logger.info(f"Pruned {len(nodes_to_remove)} small cluster nodes")
    
    def get_tree_statistics(self, tree: RaptorTree) -> Dict[str, Any]:
        """Get detailed statistics about the tree.
        
        Args:
            tree: Tree to analyze
            
        Returns:
            Dictionary of tree statistics
        """
        stats = {
            "total_nodes": tree.total_nodes,
            "total_leaves": tree.total_leaves,
            "max_level": tree.max_level,
            "num_roots": len(tree.root_ids),
            "nodes_per_level": {},
            "avg_cluster_size": 0,
            "avg_tree_depth": 0
        }
        
        # Nodes per level
        for level in range(tree.max_level + 1):
            level_nodes = tree.get_nodes_at_level(level)
            stats["nodes_per_level"][level] = len(level_nodes)
        
        # Average cluster size (non-leaf nodes)
        non_leaf_nodes = [n for n in tree.nodes.values() if not n.is_leaf()]
        if non_leaf_nodes:
            stats["avg_cluster_size"] = sum(n.size for n in non_leaf_nodes) / len(non_leaf_nodes)
        
        # Average tree depth
        leaf_nodes = [n for n in tree.nodes.values() if n.is_leaf()]
        if leaf_nodes:
            depths = [len(tree.get_ancestors(n.id)) for n in leaf_nodes]
            stats["avg_tree_depth"] = sum(depths) / len(depths)
        
        return stats
