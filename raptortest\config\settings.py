"""Configuration settings for RAPTOR Test system."""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings."""
    
    # Ollama Configuration
    ollama_base_url: str = Field(
        default="http://localhost:11434",
        env="OLLAMA_BASE_URL",
        description="Ollama server base URL"
    )
    
    embedding_model: str = Field(
        default="nomic-embed-text",
        env="EMBEDDING_MODEL",
        description="Embedding model name"
    )
    
    chat_model: str = Field(
        default="gemma3:12b",
        env="CHAT_MODEL",
        description="Chat model name"
    )
    
    # Model Parameters
    temperature: float = Field(
        default=0.1,
        env="TEMPERATURE",
        description="Model temperature for generation"
    )
    
    max_tokens: int = Field(
        default=2000,
        env="MAX_TOKENS",
        description="Maximum tokens for generation"
    )
    
    top_p: float = Field(
        default=0.9,
        env="TOP_P",
        description="Top-p sampling parameter"
    )
    
    # RAPTOR Configuration
    max_tree_levels: int = Field(
        default=3,
        env="MAX_TREE_LEVELS",
        description="Maximum levels in the RAPTOR tree"
    )
    
    clustering_method: str = Field(
        default="gmm",
        env="CLUSTERING_METHOD",
        description="Clustering method (gmm, kmeans, hierarchical)"
    )
    
    min_cluster_size: int = Field(
        default=2,
        env="MIN_CLUSTER_SIZE",
        description="Minimum cluster size"
    )
    
    max_clusters: int = Field(
        default=10,
        env="MAX_CLUSTERS",
        description="Maximum number of clusters"
    )
    
    cluster_selection_epsilon: float = Field(
        default=0.5,
        env="CLUSTER_SELECTION_EPSILON",
        description="Epsilon for cluster selection"
    )
    
    # Document Processing
    chunk_size: int = Field(
        default=1000,
        env="CHUNK_SIZE",
        description="Document chunk size"
    )
    
    chunk_overlap: int = Field(
        default=100,
        env="CHUNK_OVERLAP",
        description="Document chunk overlap"
    )
    
    max_documents: int = Field(
        default=1000,
        env="MAX_DOCUMENTS",
        description="Maximum number of documents to process"
    )
    
    # Retrieval Settings
    retrieval_k: int = Field(
        default=5,
        env="RETRIEVAL_K",
        description="Number of documents to retrieve"
    )
    
    similarity_threshold: float = Field(
        default=0.7,
        env="SIMILARITY_THRESHOLD",
        description="Similarity threshold for retrieval"
    )
    
    search_type: str = Field(
        default="similarity",
        env="SEARCH_TYPE",
        description="Search type for retrieval"
    )
    
    enable_compression: bool = Field(
        default=True,
        env="ENABLE_COMPRESSION",
        description="Enable contextual compression"
    )
    
    enable_tree_traversal: bool = Field(
        default=True,
        env="ENABLE_TREE_TRAVERSAL",
        description="Enable tree traversal retrieval"
    )
    
    # Vector Store Configuration
    vectorstore_type: str = Field(
        default="faiss",
        env="VECTORSTORE_TYPE",
        description="Vector store type"
    )
    
    vectorstore_path: str = Field(
        default="./data/vectorstore",
        env="VECTORSTORE_PATH",
        description="Vector store path"
    )
    
    index_name: str = Field(
        default="raptor_index",
        env="INDEX_NAME",
        description="Vector store index name"
    )
    
    # Tree Storage Configuration
    tree_storage_path: str = Field(
        default="./data/trees",
        env="TREE_STORAGE_PATH",
        description="Tree storage path"
    )
    
    tree_format: str = Field(
        default="json",
        env="TREE_FORMAT",
        description="Tree storage format"
    )
    
    enable_tree_cache: bool = Field(
        default=True,
        env="ENABLE_TREE_CACHE",
        description="Enable tree caching"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL",
        description="Logging level"
    )
    
    log_file: str = Field(
        default="./logs/raptor.log",
        env="LOG_FILE",
        description="Log file path"
    )
    
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT",
        description="Log format string"
    )
    
    # Performance Settings
    enable_caching: bool = Field(
        default=True,
        env="ENABLE_CACHING",
        description="Enable caching"
    )
    
    cache_ttl: int = Field(
        default=3600,
        env="CACHE_TTL",
        description="Cache TTL in seconds"
    )
    
    max_concurrent_requests: int = Field(
        default=5,
        env="MAX_CONCURRENT_REQUESTS",
        description="Maximum concurrent requests"
    )
    
    batch_size: int = Field(
        default=32,
        env="BATCH_SIZE",
        description="Batch size for processing"
    )
    
    # Evaluation Settings
    enable_evaluation: bool = Field(
        default=True,
        env="ENABLE_EVALUATION",
        description="Enable evaluation"
    )
    
    evaluation_metrics: List[str] = Field(
        default=["precision", "recall", "f1", "bleu", "rouge"],
        description="Evaluation metrics to compute"
    )
    
    ground_truth_path: str = Field(
        default="./data/evaluation/ground_truth.json",
        env="GROUND_TRUTH_PATH",
        description="Ground truth data path"
    )
    
    # Visualization Settings
    enable_visualization: bool = Field(
        default=True,
        env="ENABLE_VISUALIZATION",
        description="Enable visualization"
    )
    
    plot_dpi: int = Field(
        default=300,
        env="PLOT_DPI",
        description="Plot DPI"
    )
    
    figure_size_width: int = Field(
        default=12,
        env="FIGURE_SIZE_WIDTH",
        description="Figure width"
    )
    
    figure_size_height: int = Field(
        default=8,
        env="FIGURE_SIZE_HEIGHT",
        description="Figure height"
    )
    
    # API Configuration
    api_host: str = Field(
        default="0.0.0.0",
        env="API_HOST",
        description="API host"
    )
    
    api_port: int = Field(
        default=8000,
        env="API_PORT",
        description="API port"
    )
    
    api_workers: int = Field(
        default=1,
        env="API_WORKERS",
        description="Number of API workers"
    )
    
    # Development Settings
    debug: bool = Field(
        default=False,
        env="DEBUG",
        description="Enable debug mode"
    )
    
    enable_profiling: bool = Field(
        default=False,
        env="ENABLE_PROFILING",
        description="Enable profiling"
    )
    
    save_intermediate_results: bool = Field(
        default=False,
        env="SAVE_INTERMEDIATE_RESULTS",
        description="Save intermediate results"
    )
    
    enable_tree_validation: bool = Field(
        default=True,
        env="ENABLE_TREE_VALIDATION",
        description="Enable tree validation"
    )
    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
