#!/usr/bin/env python3
"""Debug script for RAPTOR retrieval issues."""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.services.raptor_service import RaptorService
from src.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


def debug_retrieval():
    """Debug retrieval functionality."""
    print("🔍 Debug RAPTOR Retrieval")
    print("=" * 50)
    
    # Initialize RAPTOR service
    raptor = RaptorService()
    
    # Create a simple test document
    documents = [
        """阴符经是中国古代道教经典之一。天有五贼，见之者昌。五贼在心，施行于天。这是阴符经的核心思想。"""
    ]
    
    print(f"📚 Building tree from test document...")
    tree = raptor.build_tree_from_documents(
        documents=documents,
        document_ids=["test_doc"],
        tree_id="test_tree"
    )
    
    # Check if the document has embedding
    for node_id, node in tree.nodes.items():
        print(f"Node {node_id}:")
        print(f"  Level: {node.level}")
        print(f"  Content: {node.content[:100]}...")
        print(f"  Has embedding: {node.embedding is not None}")
        if node.embedding:
            print(f"  Embedding dimension: {len(node.embedding)}")
    
    # Test query with different similarity thresholds
    query = "天有五贼是什么意思"
    print(f"\n🔍 Testing query: {query}")
    
    # Generate query embedding
    query_embedding = raptor.embedding_manager.embed_query(query)
    print(f"Query embedding dimension: {len(query_embedding)}")
    
    # Calculate similarity manually
    for node_id, node in tree.nodes.items():
        if node.embedding:
            similarity = raptor.embedding_manager.calculate_similarity(
                query_embedding, node.embedding
            )
            print(f"Similarity with node {node_id}: {similarity:.4f}")
    
    # Test retrieval with different thresholds
    thresholds = [0.1, 0.2, 0.3, 0.5, 0.7]
    for threshold in thresholds:
        # Temporarily change threshold
        original_threshold = raptor.settings.similarity_threshold
        raptor.settings.similarity_threshold = threshold
        
        result = raptor.query_tree(
            tree_id="test_tree",
            query=query,
            k=5,
            strategy="flat_retrieval",
            generate_answer=False
        )
        
        print(f"Threshold {threshold}: Retrieved {len(result['retrieved_nodes'])} nodes")
        
        # Restore original threshold
        raptor.settings.similarity_threshold = original_threshold


if __name__ == "__main__":
    setup_logging()
    debug_retrieval()
