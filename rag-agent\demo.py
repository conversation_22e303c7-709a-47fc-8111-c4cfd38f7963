#!/usr/bin/env python3
"""Simple demo script for RAG Agent system."""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.services.rag_service import RAGService
from src.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


def main():
    """Main demo function."""
    print("🚀 RAG Agent Demo")
    print("=" * 50)
    
    # Initialize RAG service
    print("Initializing RAG service...")
    rag = RAGService()
    
    # Initialize system with default documents
    print("Loading and indexing documents...")
    if not rag.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print("✅ System initialized successfully!")
    
    # Demo questions
    questions = [
        "What are the different agentic design patterns?",
        "How does reflection work in AI agents?",
        "What is tool use in AI agents?",
        "Explain multi-agent collaboration."
    ]
    
    print("\n🔍 Running demo queries...")
    print("=" * 50)
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Question {i}: {question}")
        print("-" * 40)
        
        # Ask question
        response = rag.ask(question)
        
        if response.success:
            print(f"🤖 Answer: {response.answer}")
            print(f"📊 Documents used: {len(response.documents)}")
            
            if response.metrics:
                print(f"⏱️ Processing time: {response.metrics.total_time:.2f}s")
                print(f"📈 Retrieval time: {response.metrics.retrieval_time:.2f}s")
                print(f"🧠 Generation time: {response.metrics.generation_time:.2f}s")
        else:
            print(f"❌ Error: {response.error_message}")
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    
    # Show system status
    print("\n📊 System Status:")
    status = rag.get_system_status()
    for key, value in status.items():
        if key != "collection_info":
            print(f"  {key}: {value}")


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")
