version: '3.8'

services:
  # Ollama service
  ollama:
    image: ollama/ollama:latest
    container_name: rag-agent-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RAG Agent application
  rag-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rag-agent-app
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - PYTHONPATH=/app/src
    depends_on:
      ollama:
        condition: service_healthy
    restart: unless-stopped
    command: ["python", "scripts/run_pipeline.py"]

  # Optional: ChromaDB standalone (if you want to use external ChromaDB)
  # chromadb:
  #   image: chromadb/chroma:latest
  #   container_name: rag-agent-chromadb
  #   ports:
  #     - "8001:8000"
  #   volumes:
  #     - chromadb_data:/chroma/chroma
  #   environment:
  #     - CHROMA_SERVER_HOST=0.0.0.0
  #     - CHROMA_SERVER_HTTP_PORT=8000
  #   restart: unless-stopped

volumes:
  ollama_data:
    driver: local
  # chromadb_data:
  #   driver: local

networks:
  default:
    name: rag-agent-network
