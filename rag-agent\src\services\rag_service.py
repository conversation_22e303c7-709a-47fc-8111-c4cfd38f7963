"""Main RAG service that orchestrates all components."""

import time
from typing import List, Optional, Dict, Any

from config.settings import get_settings
from config.prompts import PromptTemplates
from src.core.document_loader import DocumentLoader
from src.core.embeddings import EmbeddingManager
from src.core.retriever import DocumentRetriever
from src.core.llm import LLMManager
from src.models.schemas import (
    QueryRequest, QueryResponse, DocumentInfo, 
    RAGMetrics, RelevanceScore, HallucinationScore
)
from src.utils.logger import get_logger, PerformanceLogger
from src.utils.helpers import format_documents, timing_decorator

logger = get_logger(__name__)
performance_logger = PerformanceLogger("rag_service")


class RAGService:
    """Main RAG service that orchestrates all components."""
    
    def __init__(self):
        """Initialize the RAG service."""
        self.settings = get_settings()
        
        # Initialize core components
        self.document_loader = DocumentLoader()
        self.embedding_manager = EmbeddingManager()
        self.retriever = DocumentRetriever(self.embedding_manager)
        self.llm_manager = LLMManager()
        
        # Initialize prompt templates
        self.prompts = PromptTemplates()
        
        logger.info("RAG service initialized successfully")
    
    def initialize_system(self, urls: Optional[List[str]] = None) -> bool:
        """Initialize the system by loading and indexing documents.
        
        Args:
            urls: Optional list of URLs to load documents from
            
        Returns:
            True if initialization successful, False otherwise
        """
        logger.info("🚀 Initializing RAG system...")
        
        try:
            # Use provided URLs or default ones
            source_urls = urls or self.settings.default_urls
            
            # Load documents
            logger.info(f"Loading documents from {len(source_urls)} sources...")
            documents = self.document_loader.process_documents(
                sources=source_urls,
                source_type="url"
            )
            
            if not documents:
                logger.error("No documents were loaded")
                return False
            
            # Add documents to vector store
            logger.info("Indexing documents...")
            doc_ids = self.retriever.add_documents(documents)
            
            logger.info(f"✅ System initialized with {len(doc_ids)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize system: {e}")
            return False
    
    @timing_decorator
    def ask(self, question: str, **kwargs) -> QueryResponse:
        """Ask a question and get a response.
        
        Args:
            question: The question to ask
            **kwargs: Additional parameters
            
        Returns:
            QueryResponse object
        """
        logger.info(f"Processing question: {question}")
        start_time = time.time()
        
        try:
            # Create request object
            request = QueryRequest(question=question, **kwargs)
            
            # Process the query
            response = self._process_query(request)
            
            # Calculate total time
            total_time = time.time() - start_time
            if response.metrics:
                response.metrics.total_time = total_time
            
            performance_logger.log_timing("total_query", total_time)
            logger.info(f"Query processed successfully in {total_time:.2f} seconds")
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return QueryResponse(
                question=question,
                answer="",
                success=False,
                error_message=str(e)
            )
    
    def _process_query(self, request: QueryRequest) -> QueryResponse:
        """Process a query request.
        
        Args:
            request: Query request object
            
        Returns:
            Query response object
        """
        metrics = RAGMetrics(
            query_processing_time=0,
            retrieval_time=0,
            generation_time=0,
            total_time=0,
            documents_retrieved=0,
            documents_used=0,
            relevance_passed=0,
            hallucination_detected=False,
            response_length=0,
            response_word_count=0
        )
        
        # Step 1: Retrieve documents
        retrieval_start = time.time()
        documents = self.retriever.retrieve_documents(
            request.question, 
            k=request.max_documents
        )
        metrics.retrieval_time = time.time() - retrieval_start
        metrics.documents_retrieved = len(documents)
        
        if not documents:
            return QueryResponse(
                question=request.question,
                answer="I couldn't find any relevant documents to answer your question.",
                documents=[],
                metrics=metrics
            )
        
        # Step 2: Check relevance (if enabled)
        relevant_documents = documents
        relevance_scores = []
        
        if request.enable_quality_checks and self.settings.enable_relevance_check:
            relevant_documents, relevance_scores = self._check_relevance(
                documents, request.question
            )
            metrics.relevance_passed = len(relevant_documents)
        
        if not relevant_documents:
            return QueryResponse(
                question=request.question,
                answer="I couldn't find any relevant documents to answer your question.",
                documents=[],
                relevance_scores=relevance_scores,
                metrics=metrics
            )
        
        # Step 3: Generate answer
        generation_start = time.time()
        answer = self._generate_answer(relevant_documents, request.question)
        metrics.generation_time = time.time() - generation_start
        metrics.documents_used = len(relevant_documents)
        metrics.response_length = len(answer)
        metrics.response_word_count = len(answer.split())
        
        # Step 4: Check for hallucinations (if enabled)
        hallucination_score = None
        if request.enable_quality_checks and self.settings.enable_hallucination_check:
            hallucination_score = self._check_hallucination(
                relevant_documents, answer
            )
            metrics.hallucination_detected = (
                hallucination_score.binary_score == "no" 
                if hallucination_score else False
            )
        
        # Step 5: Create document info objects
        document_infos = [
            DocumentInfo(
                id=f"doc_{i}",
                title=doc.metadata.get('title', 'N/A'),
                source=doc.metadata.get('source', 'N/A'),
                content=doc.page_content,
                metadata=doc.metadata if request.include_metadata else {}
            )
            for i, doc in enumerate(relevant_documents)
        ]
        
        return QueryResponse(
            question=request.question,
            answer=answer,
            documents=document_infos,
            relevance_scores=relevance_scores,
            hallucination_score=hallucination_score,
            metrics=metrics
        )
    
    def _check_relevance(self, documents: List, question: str) -> tuple:
        """Check document relevance.
        
        Args:
            documents: List of documents to check
            question: The question
            
        Returns:
            Tuple of (relevant_documents, relevance_scores)
        """
        logger.debug("Checking document relevance...")
        
        relevant_docs = []
        scores = []
        
        for doc in documents:
            try:
                # Use structured output for relevance checking
                score = self.llm_manager.generate_structured_response(
                    prompt=f"Retrieved document: {doc.page_content}\n\nUser question: {question}",
                    output_schema=RelevanceScore,
                    system_message="""You are a grader assessing relevance of a retrieved document to a user question.
If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant.
It does not need to be a stringent test. The goal is to filter out erroneous retrievals.
Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question."""
                )
                
                scores.append(score)
                
                if score.binary_score == "yes":
                    relevant_docs.append(doc)
                    
            except Exception as e:
                logger.warning(f"Error checking relevance: {e}")
                # If error, assume relevant
                relevant_docs.append(doc)
                scores.append(RelevanceScore(binary_score="yes"))
        
        logger.debug(f"Found {len(relevant_docs)} relevant documents out of {len(documents)}")
        return relevant_docs, scores
    
    def _generate_answer(self, documents: List, question: str) -> str:
        """Generate answer based on documents.
        
        Args:
            documents: List of relevant documents
            question: The question
            
        Returns:
            Generated answer
        """
        logger.debug("Generating answer...")
        
        # Format documents for context
        context = format_documents(documents, include_metadata=False)
        
        # Generate answer using RAG prompt
        prompt = self.prompts.RAG_GENERATION.format(
            context=context,
            question=question
        )
        
        answer = self.llm_manager.generate_response(prompt)
        
        logger.debug(f"Generated answer: {answer[:100]}...")
        return answer
    
    def _check_hallucination(self, documents: List, answer: str) -> Optional[HallucinationScore]:
        """Check for hallucinations in the answer.
        
        Args:
            documents: Source documents
            answer: Generated answer
            
        Returns:
            Hallucination score or None if check failed
        """
        logger.debug("Checking for hallucinations...")
        
        try:
            # Format documents
            context = format_documents(documents, include_metadata=False)
            
            # Check hallucination using structured output
            score = self.llm_manager.generate_structured_response(
                prompt=f"Set of facts:\n{context}\n\nLLM generation: {answer}",
                output_schema=HallucinationScore,
                system_message="""You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts.
Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts."""
            )
            
            logger.debug(f"Hallucination check result: {score.binary_score}")
            return score
            
        except Exception as e:
            logger.warning(f"Error checking hallucination: {e}")
            return None
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status information.
        
        Returns:
            Dictionary containing system status
        """
        try:
            # Test components
            embedding_status = "OK" if self.embedding_manager.test_embedding_model() else "ERROR"
            llm_status = "OK" if self.llm_manager.test_llm() else "ERROR"
            
            # Get collection info
            collection_info = self.retriever.get_collection_info()
            
            return {
                "ollama_status": "OK",  # Assume OK if we can create LLM
                "embedding_model_status": embedding_status,
                "chat_model_status": llm_status,
                "vectorstore_status": "OK",
                "document_count": collection_info.get("document_count", 0),
                "collection_info": collection_info
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {"error": str(e)}
    
    def add_documents_from_urls(self, urls: List[str]) -> bool:
        """Add documents from URLs to the system.
        
        Args:
            urls: List of URLs to load documents from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Adding documents from {len(urls)} URLs...")
            
            documents = self.document_loader.process_documents(
                sources=urls,
                source_type="url"
            )
            
            if documents:
                doc_ids = self.retriever.add_documents(documents)
                logger.info(f"Added {len(doc_ids)} documents successfully")
                return True
            else:
                logger.warning("No documents were loaded")
                return False
                
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False
