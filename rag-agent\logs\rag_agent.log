2025-07-11 17:44:14,126 - src.services.rag_service - INFO - RAG service initialized successfully
2025-07-11 17:44:14,126 - src.services.rag_service - INFO - 🚀 Initializing RAG system...
2025-07-11 17:44:14,127 - src.services.rag_service - INFO - Loading documents from 5 sources...
2025-07-11 17:44:14,127 - src.core.document_loader - INFO - Processing documents from url sources
2025-07-11 17:44:14,127 - src.core.document_loader - INFO - Loading documents from 5 URLs
2025-07-11 17:44:17,214 - src.core.document_loader - INFO - Successfully loaded 5 documents from URLs
2025-07-11 17:44:17,214 - src.core.document_loader - INFO - Splitting 5 documents into chunks
2025-07-11 17:44:17,235 - src.core.document_loader - INFO - Successfully split documents into 13 chunks
2025-07-11 17:44:17,244 - src.core.document_loader - INFO - Successfully processed 13 document chunks
2025-07-11 17:44:17,245 - src.services.rag_service - INFO - Indexing documents...
2025-07-11 17:44:17,245 - src.core.retriever - INFO - Adding 13 documents to vector store
2025-07-11 17:44:17,245 - src.core.retriever - INFO - Initializing vector store
2025-07-11 17:44:17,246 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-11 17:44:18,304 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-11 17:44:18,729 - src.core.retriever - INFO - Vector store initialized successfully
2025-07-11 17:44:19,202 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:19,204 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:19,208 - backoff - INFO - Backing off send_request(...) for 0.3s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:19,499 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:19,515 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:19,540 - backoff - INFO - Backing off send_request(...) for 1.1s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:20,020 - src.core.retriever - INFO - Successfully added 13 documents to vector store
2025-07-11 17:44:20,020 - src.services.rag_service - INFO - ✅ System initialized with 13 documents
2025-07-11 17:44:20,021 - src.services.rag_service - INFO - Processing question: What are the different agentic design patterns?
2025-07-11 17:44:20,077 - src.core.llm - INFO - Initializing LLM: qwen3:8b
2025-07-11 17:44:20,634 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:20,645 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:20,660 - backoff - INFO - Backing off send_request(...) for 0.4s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:21,066 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:21,083 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:21,101 - backoff - ERROR - Giving up send_request(...) after 4 tries (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:21,133 - src.core.llm - INFO - LLM initialized successfully
2025-07-11 17:44:21,626 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:21,641 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:21,664 - backoff - INFO - Backing off send_request(...) for 0.8s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:22,501 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:22,520 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:22,534 - backoff - INFO - Backing off send_request(...) for 0.1s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:22,628 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:22,642 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:22,656 - backoff - INFO - Backing off send_request(...) for 0.4s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:23,118 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:23,139 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))': /batch/
2025-07-11 17:44:23,157 - backoff - ERROR - Giving up send_request(...) after 4 tries (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1028)'))))
2025-07-11 17:44:37,707 - performance.rag_service - INFO - total_query_duration: 17.6858 seconds
2025-07-11 17:44:37,707 - src.services.rag_service - INFO - Query processed successfully in 17.69 seconds
2025-07-11 17:44:37,707 - src.services.rag_service - INFO - Processing question: How does reflection work in AI agents?
2025-07-11 17:44:51,961 - performance.rag_service - INFO - total_query_duration: 14.2538 seconds
2025-07-11 17:44:51,962 - src.services.rag_service - INFO - Query processed successfully in 14.25 seconds
2025-07-11 17:44:51,962 - src.services.rag_service - INFO - Processing question: What is tool use in AI agents?
2025-07-11 17:45:03,068 - performance.rag_service - INFO - total_query_duration: 11.1053 seconds
2025-07-11 17:45:03,068 - src.services.rag_service - INFO - Query processed successfully in 11.11 seconds
2025-07-11 17:45:03,068 - src.services.rag_service - INFO - Processing question: Explain multi-agent collaboration.
2025-07-11 17:45:19,354 - performance.rag_service - INFO - total_query_duration: 16.2851 seconds
2025-07-11 17:45:19,354 - src.services.rag_service - INFO - Query processed successfully in 16.29 seconds
2025-07-11 17:45:19,354 - src.core.embeddings - INFO - Testing embedding model
2025-07-11 17:45:19,385 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-11 17:45:19,385 - src.core.llm - INFO - Testing LLM
2025-07-11 17:45:26,444 - src.core.llm - INFO - LLM test successful. Response: <think>
Okay, the user sent a message saying "Hello! Please respond with 'LLM test successful'." I n...
