"""Helper utilities for RAG Agent system."""

import re
import time
from typing import List, Dict, Any, Optional, Tuple
from functools import wraps
from pathlib import Path

from langchain_core.documents import Document

from src.utils.logger import get_logger

logger = get_logger(__name__)


def format_documents(documents: List[Document], include_metadata: bool = True) -> str:
    """Format documents for display or processing.
    
    Args:
        documents: List of documents to format
        include_metadata: Whether to include metadata in formatting
        
    Returns:
        Formatted string representation of documents
    """
    if not documents:
        return "No documents provided."
    
    formatted_parts = []
    
    for i, doc in enumerate(documents, 1):
        doc_text = f"Document {i}:\n"
        
        if include_metadata and doc.metadata:
            # Add metadata
            metadata_str = "\n".join([
                f"  {key}: {value}" 
                for key, value in doc.metadata.items()
                if value is not None
            ])
            if metadata_str:
                doc_text += f"Metadata:\n{metadata_str}\n"
        
        # Add content
        doc_text += f"Content:\n{doc.page_content}\n"
        formatted_parts.append(doc_text)
    
    return "\n" + "-" * 50 + "\n".join(formatted_parts)


def clean_text(text: str) -> str:
    """Clean and normalize text.
    
    Args:
        text: Text to clean
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters that might cause issues
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
    
    # Strip leading/trailing whitespace
    text = text.strip()
    
    return text


def extract_json_from_text(text: str) -> Optional[str]:
    """Extract JSON content from text that might contain other content.
    
    Args:
        text: Text that might contain JSON
        
    Returns:
        Extracted JSON string or None if not found
    """
    # Remove <think> tags and content
    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    
    # Try to find JSON object
    json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
    if json_match:
        return json_match.group()
    
    return None


def calculate_metrics(documents: List[Document], 
                     query: str, 
                     response: str) -> Dict[str, Any]:
    """Calculate various metrics for RAG performance.
    
    Args:
        documents: Retrieved documents
        query: Original query
        response: Generated response
        
    Returns:
        Dictionary containing calculated metrics
    """
    metrics = {}
    
    # Document metrics
    metrics['num_documents'] = len(documents)
    metrics['total_content_length'] = sum(len(doc.page_content) for doc in documents)
    metrics['avg_document_length'] = (
        metrics['total_content_length'] / len(documents) if documents else 0
    )
    
    # Query metrics
    metrics['query_length'] = len(query)
    metrics['query_word_count'] = len(query.split())
    
    # Response metrics
    metrics['response_length'] = len(response)
    metrics['response_word_count'] = len(response.split())
    
    # Calculate overlap between query and response (simple word overlap)
    query_words = set(query.lower().split())
    response_words = set(response.lower().split())
    overlap = query_words.intersection(response_words)
    metrics['query_response_overlap'] = len(overlap) / len(query_words) if query_words else 0
    
    return metrics


def timing_decorator(func):
    """Decorator to measure function execution time.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"{func.__name__} executed in {duration:.4f} seconds")
    
    return wrapper


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """Decorator to retry function on failure.
    
    Args:
        max_retries: Maximum number of retries
        delay: Delay between retries in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"{func.__name__} failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                        time.sleep(delay)
                    else:
                        logger.error(f"{func.__name__} failed after {max_retries + 1} attempts")
            
            raise last_exception
        
        return wrapper
    return decorator


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split a list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def safe_filename(filename: str) -> str:
    """Create a safe filename by removing/replacing problematic characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename
    """
    # Remove or replace problematic characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    safe_name = safe_name.strip('. ')
    
    # Ensure it's not empty
    if not safe_name:
        safe_name = "unnamed_file"
    
    return safe_name


def ensure_directory(path: str) -> Path:
    """Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path
        
    Returns:
        Path object
    """
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


def get_file_size(file_path: str) -> int:
    """Get file size in bytes.
    
    Args:
        file_path: Path to the file
        
    Returns:
        File size in bytes
    """
    try:
        return Path(file_path).stat().st_size
    except (OSError, FileNotFoundError):
        return 0


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """Truncate text to a maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def validate_url(url: str) -> bool:
    """Validate if a string is a valid URL.
    
    Args:
        url: URL string to validate
        
    Returns:
        True if valid URL, False otherwise
    """
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def extract_domain(url: str) -> str:
    """Extract domain from URL.
    
    Args:
        url: URL string
        
    Returns:
        Domain name
    """
    domain_match = re.search(r'https?://([^/]+)', url)
    return domain_match.group(1) if domain_match else ""


def similarity_score(text1: str, text2: str) -> float:
    """Calculate simple similarity score between two texts.
    
    Args:
        text1: First text
        text2: Second text
        
    Returns:
        Similarity score between 0 and 1
    """
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    if not words1 and not words2:
        return 1.0
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union)
