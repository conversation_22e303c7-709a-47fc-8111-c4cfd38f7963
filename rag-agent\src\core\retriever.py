"""Document retrieval functionality."""

from typing import List, Optional, Dict, Any
import logging
import os
from pathlib import Path

from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStoreRetriever

from config.settings import get_settings
from src.core.embeddings import EmbeddingManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DocumentRetriever:
    """Manages document retrieval using vector similarity search."""
    
    def __init__(self, embedding_manager: Optional[EmbeddingManager] = None):
        """Initialize the document retriever.
        
        Args:
            embedding_manager: Optional embedding manager instance
        """
        self.settings = get_settings()
        self.embedding_manager = embedding_manager or EmbeddingManager()
        self._vectorstore = None
        self._retriever = None
        
    @property
    def vectorstore(self) -> Chroma:
        """Get or create the vector store."""
        if self._vectorstore is None:
            logger.info("Initializing vector store")
            try:
                # Ensure the vectorstore directory exists
                vectorstore_path = Path(self.settings.vectorstore_path)
                vectorstore_path.mkdir(parents=True, exist_ok=True)
                
                self._vectorstore = Chroma(
                    collection_name=self.settings.collection_name,
                    embedding_function=self.embedding_manager.embedding_model,
                    persist_directory=str(vectorstore_path)
                )
                logger.info("Vector store initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize vector store: {e}")
                raise
        return self._vectorstore
    
    @property
    def retriever(self) -> VectorStoreRetriever:
        """Get or create the retriever."""
        if self._retriever is None:
            logger.info("Initializing retriever")
            self._retriever = self.vectorstore.as_retriever(
                search_type=self.settings.search_type,
                search_kwargs={'k': self.settings.retrieval_k}
            )
            logger.info("Retriever initialized successfully")
        return self._retriever
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """Add documents to the vector store.
        
        Args:
            documents: List of documents to add
            
        Returns:
            List of document IDs
        """
        logger.info(f"Adding {len(documents)} documents to vector store")
        
        try:
            # Add documents to the vector store
            doc_ids = self.vectorstore.add_documents(documents)
            
            # Persist the vector store
            if hasattr(self.vectorstore, 'persist'):
                self.vectorstore.persist()
                
            logger.info(f"Successfully added {len(doc_ids)} documents to vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents to vector store: {e}")
            raise
    
    def retrieve_documents(self, query: str, k: Optional[int] = None) -> List[Document]:
        """Retrieve relevant documents for a query.
        
        Args:
            query: Search query
            k: Number of documents to retrieve (optional)
            
        Returns:
            List of relevant documents
        """
        logger.debug(f"Retrieving documents for query: {query[:100]}...")
        
        try:
            if k is not None:
                # Create a temporary retriever with custom k
                temp_retriever = self.vectorstore.as_retriever(
                    search_type=self.settings.search_type,
                    search_kwargs={'k': k}
                )
                documents = temp_retriever.invoke(query)
            else:
                documents = self.retriever.invoke(query)
            
            logger.debug(f"Retrieved {len(documents)} documents")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to retrieve documents: {e}")
            return []
    
    def similarity_search(self, query: str, k: int = 4, 
                         score_threshold: Optional[float] = None) -> List[Document]:
        """Perform similarity search with optional score threshold.
        
        Args:
            query: Search query
            k: Number of documents to retrieve
            score_threshold: Minimum similarity score threshold
            
        Returns:
            List of relevant documents
        """
        logger.debug(f"Performing similarity search for: {query[:100]}...")
        
        try:
            if score_threshold is not None:
                # Use similarity search with score
                docs_with_scores = self.vectorstore.similarity_search_with_score(
                    query, k=k
                )
                # Filter by score threshold
                documents = [
                    doc for doc, score in docs_with_scores 
                    if score >= score_threshold
                ]
            else:
                documents = self.vectorstore.similarity_search(query, k=k)
            
            logger.debug(f"Found {len(documents)} documents above threshold")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the vector store collection.
        
        Returns:
            Dictionary containing collection information
        """
        try:
            collection = self.vectorstore._collection
            count = collection.count()
            
            return {
                "collection_name": self.settings.collection_name,
                "document_count": count,
                "embedding_dimension": self.embedding_manager.get_embedding_dimension(),
                "vectorstore_path": self.settings.vectorstore_path
            }
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}
    
    def delete_collection(self) -> bool:
        """Delete the entire collection.
        
        Returns:
            True if successful, False otherwise
        """
        logger.warning("Deleting vector store collection")
        
        try:
            if self._vectorstore is not None:
                self._vectorstore.delete_collection()
                self._vectorstore = None
                self._retriever = None
                
            # Also remove the persist directory if it exists
            vectorstore_path = Path(self.settings.vectorstore_path)
            if vectorstore_path.exists():
                import shutil
                shutil.rmtree(vectorstore_path)
                
            logger.info("Vector store collection deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection: {e}")
            return False
    
    def update_retrieval_settings(self, k: Optional[int] = None, 
                                search_type: Optional[str] = None) -> None:
        """Update retrieval settings.
        
        Args:
            k: Number of documents to retrieve
            search_type: Type of search to perform
        """
        logger.info("Updating retrieval settings")
        
        search_kwargs = {'k': k or self.settings.retrieval_k}
        search_type = search_type or self.settings.search_type
        
        self._retriever = self.vectorstore.as_retriever(
            search_type=search_type,
            search_kwargs=search_kwargs
        )
        
        logger.info(f"Updated retrieval settings: k={search_kwargs['k']}, search_type={search_type}")
    
    def export_documents(self, output_path: str) -> bool:
        """Export all documents from the vector store.
        
        Args:
            output_path: Path to save the exported documents
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Exporting documents to {output_path}")
        
        try:
            # This is a simplified export - in practice, you might want to
            # implement a more sophisticated export mechanism
            collection_info = self.get_collection_info()
            
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(collection_info, f, indent=2, ensure_ascii=False)
            
            logger.info("Documents exported successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export documents: {e}")
            return False
