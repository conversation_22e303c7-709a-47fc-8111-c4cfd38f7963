# Ollama 设置指南

## 1. 安装 Ollama

### Windows
1. 访问 [Ollama 官网](https://ollama.ai/)
2. 下载 Windows 版本的安装程序
3. 运行安装程序并按照提示完成安装

### macOS
```bash
# 使用 Homebrew 安装
brew install ollama

# 或者下载官方安装包
# 访问 https://ollama.ai/ 下载 .dmg 文件
```

### Linux
```bash
# 使用官方安装脚本
curl -fsSL https://ollama.ai/install.sh | sh
```

## 2. 启动 Ollama 服务

### Windows
- Ollama 安装后会自动启动服务
- 可以在系统托盘中看到 Ollama 图标
- 默认运行在 `http://localhost:11434`

### macOS/Linux
```bash
# 启动 Ollama 服务
ollama serve
```

## 3. 安装所需模型

```bash
# 安装嵌入模型（用于向量化文档）
ollama pull nomic-embed-text

# 安装聊天模型（用于生成回答）
ollama pull llama3.2

# 可选：安装其他模型
ollama pull mistral
ollama pull codellama
ollama pull qwen2.5
```

## 4. 验证安装

```bash
# 检查 Ollama 是否正在运行
curl http://localhost:11434/api/tags

# 列出已安装的模型
ollama list

# 测试模型
ollama run llama3.2 "Hello, how are you?"
```

## 5. 安装 Python 依赖

```bash
# 安装 langchain-ollama
pip install langchain-ollama

# 如果需要，也可以安装其他相关包
pip install langchain-community
pip install chromadb
```

## 6. 模型推荐

### 嵌入模型
- **nomic-embed-text**: 高质量的英文嵌入模型，适合 RAG 应用
- **mxbai-embed-large**: 另一个优秀的嵌入模型选择

### 聊天模型
- **llama3.2**: Meta 的最新模型，平衡性能和质量
- **mistral**: 快速且高效的模型
- **qwen2.5**: 阿里巴巴的模型，对中文支持较好
- **codellama**: 专门用于代码生成的模型

## 7. 配置说明

在 `rag-ag.py` 中的配置：

```python
# 嵌入模型配置
embedding_model = OllamaEmbeddings(
    model="nomic-embed-text",
    base_url="http://localhost:11434"
)

# 聊天模型配置
llm = ChatOllama(
    model="llama3.2",
    base_url="http://localhost:11434",
    temperature=0.1  # 控制回答的随机性
)
```

## 8. 常见问题

### Q: Ollama 服务无法启动
A: 检查端口 11434 是否被占用，或尝试重启 Ollama 服务

### Q: 模型下载速度慢
A: 可以设置代理或使用国内镜像源

### Q: 内存不足
A: 选择较小的模型，如 `llama3.2:1b` 而不是 `llama3.2:8b`

### Q: 如何更换模型
A: 修改代码中的 `model` 参数即可，确保该模型已通过 `ollama pull` 下载

## 9. 性能优化

- 使用 GPU 加速（如果可用）
- 调整模型大小以适应硬件配置
- 设置合适的 `temperature` 和其他参数
- 考虑使用量化模型以减少内存使用

## 10. 更多资源

- [Ollama 官方文档](https://github.com/ollama/ollama)
- [LangChain Ollama 集成文档](https://python.langchain.com/docs/integrations/llms/ollama)
- [模型库](https://ollama.ai/library)
