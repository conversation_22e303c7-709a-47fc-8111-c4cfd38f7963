#!/usr/bin/env python3
"""Simple demo script for RAPTOR Test system."""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.utils.logger import setup_logging, get_logger
from src.core.embeddings import EmbeddingManager
from src.core.clustering import ClusteringManager
from src.core.summarization import SummarizationManager
from src.models.tree_node import TreeNode, RaptorTree
import numpy as np

logger = get_logger(__name__)


def test_embeddings():
    """Test embedding functionality."""
    print("🔍 Testing Embeddings...")
    
    embedding_manager = EmbeddingManager()
    
    # Test embedding model
    if not embedding_manager.test_embedding_model():
        print("❌ Embedding model test failed")
        return False
    
    # Test document embeddings
    test_docs = [
        "The greenhouse effect is a natural process that warms the Earth's surface.",
        "Climate change refers to long-term shifts in global temperatures and weather patterns.",
        "Renewable energy sources include solar, wind, and hydroelectric power."
    ]
    
    embeddings = embedding_manager.embed_documents(test_docs)
    print(f"✅ Generated embeddings for {len(test_docs)} documents")
    print(f"   Embedding dimension: {len(embeddings[0])}")
    
    return True


def test_clustering():
    """Test clustering functionality."""
    print("\n🔗 Testing Clustering...")
    
    # Create sample embeddings
    np.random.seed(42)
    embeddings = np.random.rand(10, 768)  # 10 documents, 768-dim embeddings
    
    clustering_manager = ClusteringManager()
    
    # Test clustering
    labels, metadata = clustering_manager.perform_clustering(embeddings)
    
    print(f"✅ Clustering completed")
    print(f"   Method: {metadata['method']}")
    print(f"   Number of clusters: {metadata['n_clusters']}")
    print(f"   Silhouette score: {metadata.get('silhouette_score', 'N/A')}")
    
    # Validate clustering
    is_valid = clustering_manager.validate_clustering(embeddings, labels)
    print(f"   Validation: {'✅ Passed' if is_valid else '❌ Failed'}")
    
    return True


def test_summarization():
    """Test summarization functionality."""
    print("\n📝 Testing Summarization...")
    
    summarization_manager = SummarizationManager()
    
    # Test summarization model
    if not summarization_manager.test_summarization():
        print("❌ Summarization model test failed")
        return False
    
    # Test text summarization
    test_text = """
    The greenhouse effect is a natural process that occurs when certain gases in Earth's atmosphere 
    trap heat from the sun. These gases, known as greenhouse gases, include carbon dioxide, methane, 
    water vapor, and others. While the greenhouse effect is essential for maintaining Earth's 
    temperature at levels suitable for life, human activities have increased the concentration of 
    these gases, leading to enhanced warming and climate change. The primary sources of increased 
    greenhouse gases include burning fossil fuels, deforestation, and industrial processes.
    """
    
    summary = summarization_manager.summarize_text(test_text.strip())
    print(f"✅ Text summarization completed")
    print(f"   Original length: {len(test_text.strip())} characters")
    print(f"   Summary length: {len(summary)} characters")
    print(f"   Summary: {summary[:100]}...")
    
    return True


def test_tree_structure():
    """Test tree structure functionality."""
    print("\n🌳 Testing Tree Structure...")
    
    # Create a simple tree
    tree = RaptorTree()
    
    # Create leaf nodes
    leaf1 = TreeNode(
        level=0,
        content="Document 1: Climate change basics",
        document_id="doc1"
    )
    leaf2 = TreeNode(
        level=0,
        content="Document 2: Greenhouse effect explanation",
        document_id="doc2"
    )
    leaf3 = TreeNode(
        level=0,
        content="Document 3: Renewable energy overview",
        document_id="doc3"
    )
    
    # Create intermediate node
    intermediate = TreeNode(
        level=1,
        content="Summary: Environmental topics cluster",
        cluster_id=1
    )
    intermediate.add_child(leaf1.id)
    intermediate.add_child(leaf2.id)
    leaf1.parent_id = intermediate.id
    leaf2.parent_id = intermediate.id
    
    # Create root node
    root = TreeNode(
        level=2,
        content="Root summary: All environmental documents",
        cluster_id=0
    )
    root.add_child(intermediate.id)
    root.add_child(leaf3.id)
    intermediate.parent_id = root.id
    leaf3.parent_id = root.id
    
    # Add nodes to tree
    tree.add_node(leaf1)
    tree.add_node(leaf2)
    tree.add_node(leaf3)
    tree.add_node(intermediate)
    tree.add_node(root)
    
    print(f"✅ Tree structure created")
    print(f"   Total nodes: {tree.total_nodes}")
    print(f"   Total leaves: {tree.total_leaves}")
    print(f"   Max level: {tree.max_level}")
    print(f"   Root nodes: {len(tree.root_ids)}")
    
    # Validate tree
    validation = tree.validate_tree()
    print(f"   Validation: {'✅ Passed' if validation['is_valid'] else '❌ Failed'}")
    
    if not validation['is_valid']:
        for issue in validation['issues']:
            print(f"     Issue: {issue}")
    
    return validation['is_valid']


def main():
    """Main demo function."""
    print("🚀 RAPTOR Test Demo")
    print("=" * 50)
    
    try:
        # Test individual components
        tests = [
            ("Embeddings", test_embeddings),
            ("Clustering", test_clustering),
            ("Summarization", test_summarization),
            ("Tree Structure", test_tree_structure),
        ]
        
        results = {}
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name} test failed: {e}")
                results[test_name] = False
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        print("=" * 50)
        
        passed = 0
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{len(tests)} tests passed")
        
        if passed == len(tests):
            print("🎉 All tests passed! RAPTOR system is ready.")
        else:
            print("⚠️ Some tests failed. Please check the configuration.")
        
        return passed == len(tests)
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")
        return False


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")
        sys.exit(1)
