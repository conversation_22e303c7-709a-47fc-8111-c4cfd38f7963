"""LLM management functionality."""

from typing import Optional, Dict, Any, List
import logging
import time

from langchain_ollama import ChatOllama
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.outputs import LLMResult

from config.settings import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class LLMManager:
    """Manages Large Language Model interactions using Ollama."""
    
    def __init__(self):
        """Initialize the LLM manager."""
        self.settings = get_settings()
        self._llm = None
        
    @property
    def llm(self) -> ChatOllama:
        """Get or create the LLM instance."""
        if self._llm is None:
            logger.info(f"Initializing LLM: {self.settings.chat_model}")
            try:
                self._llm = ChatOllama(
                    model=self.settings.chat_model,
                    base_url=self.settings.ollama_base_url,
                    temperature=self.settings.temperature,
                    # max_tokens=self.settings.max_tokens,  # Note: Ollama might not support this parameter
                )
                logger.info("LLM initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize LLM: {e}")
                raise
        return self._llm
    
    def generate_response(self, prompt: str, 
                         system_message: Optional[str] = None) -> str:
        """Generate a response from the LLM.
        
        Args:
            prompt: User prompt
            system_message: Optional system message
            
        Returns:
            Generated response
        """
        logger.debug(f"Generating response for prompt: {prompt[:100]}...")
        start_time = time.time()
        
        try:
            messages = []
            if system_message:
                messages.append(SystemMessage(content=system_message))
            messages.append(HumanMessage(content=prompt))
            
            response = self.llm.invoke(messages)
            
            elapsed_time = time.time() - start_time
            logger.debug(f"Response generated in {elapsed_time:.2f} seconds")
            
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise
    
    def generate_structured_response(self, prompt: str, 
                                   output_schema: Any,
                                   system_message: Optional[str] = None) -> Any:
        """Generate a structured response from the LLM.
        
        Args:
            prompt: User prompt
            output_schema: Pydantic model for structured output
            system_message: Optional system message
            
        Returns:
            Structured response object
        """
        logger.debug(f"Generating structured response for prompt: {prompt[:100]}...")
        
        try:
            structured_llm = self.llm.with_structured_output(output_schema)
            
            messages = []
            if system_message:
                messages.append(SystemMessage(content=system_message))
            messages.append(HumanMessage(content=prompt))
            
            response = structured_llm.invoke(messages)
            logger.debug("Structured response generated successfully")
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate structured response: {e}")
            raise
    
    def test_llm(self) -> bool:
        """Test if the LLM is working correctly.
        
        Returns:
            True if the LLM is working, False otherwise
        """
        logger.info("Testing LLM")
        
        try:
            test_prompt = "Hello! Please respond with 'LLM test successful'."
            response = self.generate_response(test_prompt)
            
            if response and len(response.strip()) > 0:
                logger.info(f"LLM test successful. Response: {response[:100]}...")
                return True
            else:
                logger.error("LLM test failed: empty response")
                return False
                
        except Exception as e:
            logger.error(f"LLM test failed: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model.
        
        Returns:
            Dictionary containing model information
        """
        return {
            "model_name": self.settings.chat_model,
            "base_url": self.settings.ollama_base_url,
            "temperature": self.settings.temperature,
            "max_tokens": self.settings.max_tokens,
            "top_p": self.settings.top_p
        }
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate the number of tokens in a text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        # Simple estimation: roughly 4 characters per token
        return len(text) // 4
    
    def truncate_to_token_limit(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            max_tokens: Maximum number of tokens
            
        Returns:
            Truncated text
        """
        estimated_tokens = self.estimate_tokens(text)
        
        if estimated_tokens <= max_tokens:
            return text
        
        # Calculate approximate character limit
        char_limit = max_tokens * 4
        truncated = text[:char_limit]
        
        # Try to truncate at word boundary
        last_space = truncated.rfind(' ')
        if last_space > char_limit * 0.8:  # If we can find a space in the last 20%
            truncated = truncated[:last_space]
        
        logger.warning(f"Text truncated from {len(text)} to {len(truncated)} characters")
        return truncated
    
    def batch_generate(self, prompts: List[str], 
                      system_message: Optional[str] = None) -> List[str]:
        """Generate responses for multiple prompts.
        
        Args:
            prompts: List of prompts
            system_message: Optional system message
            
        Returns:
            List of generated responses
        """
        logger.info(f"Generating responses for {len(prompts)} prompts")
        
        responses = []
        for i, prompt in enumerate(prompts):
            logger.debug(f"Processing prompt {i+1}/{len(prompts)}")
            try:
                response = self.generate_response(prompt, system_message)
                responses.append(response)
            except Exception as e:
                logger.error(f"Failed to generate response for prompt {i+1}: {e}")
                responses.append("")
        
        logger.info(f"Generated {len(responses)} responses")
        return responses
