"""Prompt templates for RAPTOR Test system."""

from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate


class PromptTemplates:
    """Collection of prompt templates used in the RAPTOR system."""
    
    # Document Summarization Prompt
    SUMMARIZATION = """You are an expert at creating concise, informative summaries.
Please summarize the following text, capturing the key points and main ideas.
The summary should be comprehensive yet concise, maintaining the essential information.

Text to summarize:
{text}

Summary:"""
    
    # Cluster Summarization Prompt
    CLUSTER_SUMMARIZATION = """You are an expert at synthesizing information from multiple related documents.
Please create a comprehensive summary that captures the common themes and key information 
from the following related documents. The summary should:

1. Identify the main themes and topics
2. Synthesize the key information
3. Maintain important details
4. Be coherent and well-structured

Documents:
{documents}

Comprehensive Summary:"""
    
    # Query Answering Prompt
    QUERY_ANSWERING = """You are a helpful assistant that answers questions based on the provided context.
Use only the information from the context to answer the question. If the context doesn't contain 
enough information to answer the question, say so clearly.

Context:
{context}

Question: {question}

Answer:"""
    
    # Contextual Compression Prompt
    CONTEXTUAL_COMPRESSION = """Given the following context and question, extract only the most relevant 
information that would be useful for answering the question. Remove any irrelevant details while 
preserving all information that could help answer the question.

Context: {context}
Question: {question}

Relevant Information:"""
    
    # Tree Level Summary Prompt
    TREE_LEVEL_SUMMARY = """You are creating a hierarchical summary for a document tree structure.
This summary will represent a cluster of documents at level {level} of the tree.
Create a summary that:

1. Captures the main themes from all documents in this cluster
2. Maintains hierarchical coherence with parent and child nodes
3. Is detailed enough to be useful for retrieval
4. Preserves important factual information

Documents in cluster:
{documents}

Cluster Summary (Level {level}):"""
    
    # Document Relevance Assessment Prompt
    RELEVANCE_ASSESSMENT = """Assess the relevance of the following document to the given query.
Rate the relevance on a scale of 0-1, where:
- 0 = Not relevant at all
- 0.5 = Somewhat relevant
- 1 = Highly relevant

Also provide a brief explanation for your rating.

Query: {query}
Document: {document}

Relevance Score (0-1):
Explanation:"""
    
    # Tree Structure Validation Prompt
    TREE_VALIDATION = """Evaluate the quality and coherence of this document tree structure.
Consider:
1. Logical hierarchy
2. Coherent clustering
3. Summary quality
4. Information preservation

Tree Structure:
{tree_structure}

Evaluation:"""
    
    # Multi-level Retrieval Prompt
    MULTI_LEVEL_RETRIEVAL = """You are performing multi-level retrieval in a hierarchical document tree.
Based on the query, determine which levels of the tree are most relevant and should be explored.

Query: {query}
Available Levels: {levels}
Tree Structure Summary: {tree_summary}

Recommended Retrieval Strategy:"""
    
    @classmethod
    def get_summarization_prompt(cls) -> ChatPromptTemplate:
        """Get summarization prompt template."""
        return ChatPromptTemplate.from_template(cls.SUMMARIZATION)
    
    @classmethod
    def get_cluster_summarization_prompt(cls) -> ChatPromptTemplate:
        """Get cluster summarization prompt template."""
        return ChatPromptTemplate.from_template(cls.CLUSTER_SUMMARIZATION)
    
    @classmethod
    def get_query_answering_prompt(cls) -> ChatPromptTemplate:
        """Get query answering prompt template."""
        return ChatPromptTemplate.from_template(cls.QUERY_ANSWERING)
    
    @classmethod
    def get_contextual_compression_prompt(cls) -> ChatPromptTemplate:
        """Get contextual compression prompt template."""
        return ChatPromptTemplate.from_template(cls.CONTEXTUAL_COMPRESSION)
    
    @classmethod
    def get_tree_level_summary_prompt(cls) -> ChatPromptTemplate:
        """Get tree level summary prompt template."""
        return ChatPromptTemplate.from_template(cls.TREE_LEVEL_SUMMARY)
    
    @classmethod
    def get_relevance_assessment_prompt(cls) -> ChatPromptTemplate:
        """Get relevance assessment prompt template."""
        return ChatPromptTemplate.from_template(cls.RELEVANCE_ASSESSMENT)
    
    @classmethod
    def get_tree_validation_prompt(cls) -> ChatPromptTemplate:
        """Get tree validation prompt template."""
        return ChatPromptTemplate.from_template(cls.TREE_VALIDATION)
    
    @classmethod
    def get_multi_level_retrieval_prompt(cls) -> ChatPromptTemplate:
        """Get multi-level retrieval prompt template."""
        return ChatPromptTemplate.from_template(cls.MULTI_LEVEL_RETRIEVAL)
    
    @classmethod
    def get_custom_prompt(cls, template: str, **kwargs) -> ChatPromptTemplate:
        """Create a custom prompt template."""
        return ChatPromptTemplate.from_template(template.format(**kwargs))
