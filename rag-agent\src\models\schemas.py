"""Pydantic schemas for RAG Agent system."""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class QueryRequest(BaseModel):
    """Request model for RAG queries."""
    
    question: str = Field(..., description="The question to ask")
    max_documents: Optional[int] = Field(
        default=4, 
        description="Maximum number of documents to retrieve"
    )
    include_metadata: Optional[bool] = Field(
        default=True,
        description="Whether to include document metadata in response"
    )
    enable_quality_checks: Optional[bool] = Field(
        default=True,
        description="Whether to enable quality checks (relevance, hallucination)"
    )
    
    @validator('question')
    def question_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Question cannot be empty')
        return v.strip()


class DocumentInfo(BaseModel):
    """Information about a retrieved document."""
    
    id: str = Field(..., description="Document ID")
    title: Optional[str] = Field(None, description="Document title")
    source: Optional[str] = Field(None, description="Document source")
    content: str = Field(..., description="Document content")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Document metadata"
    )
    relevance_score: Optional[float] = Field(
        None,
        description="Relevance score for the query"
    )


class RelevanceScore(BaseModel):
    """Binary score for document relevance."""
    
    binary_score: str = Field(
        ...,
        description="Documents are relevant to the question, 'yes' or 'no'"
    )
    
    @validator('binary_score')
    def validate_binary_score(cls, v):
        if v.lower() not in ['yes', 'no']:
            raise ValueError("Binary score must be 'yes' or 'no'")
        return v.lower()


class HallucinationScore(BaseModel):
    """Binary score for hallucination detection."""
    
    binary_score: str = Field(
        ...,
        description="Answer is grounded in the facts, 'yes' or 'no'"
    )
    
    @validator('binary_score')
    def validate_binary_score(cls, v):
        if v.lower() not in ['yes', 'no']:
            raise ValueError("Binary score must be 'yes' or 'no'")
        return v.lower()


class HighlightedDocuments(BaseModel):
    """Highlighted document segments used for answer generation."""
    
    id: List[str] = Field(
        ...,
        description="List of document IDs used to answer the question"
    )
    title: List[str] = Field(
        ...,
        description="List of document titles used to answer the question"
    )
    source: List[str] = Field(
        ...,
        description="List of document sources used to answer the question"
    )
    segment: List[str] = Field(
        ...,
        description="List of document segments that directly answer the question"
    )
    
    @validator('id', 'title', 'source', 'segment')
    def lists_same_length(cls, v, values):
        if 'id' in values and len(v) != len(values['id']):
            raise ValueError('All lists must have the same length')
        return v


class RAGMetrics(BaseModel):
    """Metrics for RAG system performance."""
    
    query_processing_time: float = Field(
        ...,
        description="Time taken to process the query (seconds)"
    )
    retrieval_time: float = Field(
        ...,
        description="Time taken for document retrieval (seconds)"
    )
    generation_time: float = Field(
        ...,
        description="Time taken for answer generation (seconds)"
    )
    total_time: float = Field(
        ...,
        description="Total processing time (seconds)"
    )
    documents_retrieved: int = Field(
        ...,
        description="Number of documents retrieved"
    )
    documents_used: int = Field(
        ...,
        description="Number of documents actually used for generation"
    )
    relevance_passed: int = Field(
        ...,
        description="Number of documents that passed relevance check"
    )
    hallucination_detected: bool = Field(
        ...,
        description="Whether hallucination was detected in the response"
    )
    response_length: int = Field(
        ...,
        description="Length of generated response in characters"
    )
    response_word_count: int = Field(
        ...,
        description="Word count of generated response"
    )


class QueryResponse(BaseModel):
    """Response model for RAG queries."""
    
    question: str = Field(..., description="The original question")
    answer: str = Field(..., description="The generated answer")
    documents: List[DocumentInfo] = Field(
        default_factory=list,
        description="Retrieved documents used for the answer"
    )
    highlighted_segments: Optional[HighlightedDocuments] = Field(
        None,
        description="Specific document segments used for the answer"
    )
    relevance_scores: Optional[List[RelevanceScore]] = Field(
        None,
        description="Relevance scores for retrieved documents"
    )
    hallucination_score: Optional[HallucinationScore] = Field(
        None,
        description="Hallucination detection result"
    )
    metrics: Optional[RAGMetrics] = Field(
        None,
        description="Performance metrics"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="Response timestamp"
    )
    success: bool = Field(True, description="Whether the query was successful")
    error_message: Optional[str] = Field(
        None,
        description="Error message if query failed"
    )


class SystemStatus(BaseModel):
    """System status information."""
    
    ollama_status: str = Field(..., description="Ollama service status")
    embedding_model_status: str = Field(..., description="Embedding model status")
    chat_model_status: str = Field(..., description="Chat model status")
    vectorstore_status: str = Field(..., description="Vector store status")
    document_count: int = Field(..., description="Number of documents in vector store")
    last_updated: datetime = Field(..., description="Last system update timestamp")


class ConfigUpdate(BaseModel):
    """Configuration update request."""
    
    retrieval_k: Optional[int] = Field(None, description="Number of documents to retrieve")
    temperature: Optional[float] = Field(None, description="Model temperature")
    chunk_size: Optional[int] = Field(None, description="Document chunk size")
    enable_relevance_check: Optional[bool] = Field(None, description="Enable relevance checking")
    enable_hallucination_check: Optional[bool] = Field(None, description="Enable hallucination checking")
    
    @validator('retrieval_k')
    def validate_retrieval_k(cls, v):
        if v is not None and (v < 1 or v > 20):
            raise ValueError('retrieval_k must be between 1 and 20')
        return v
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if v is not None and (v < 0 or v > 2):
            raise ValueError('temperature must be between 0 and 2')
        return v
    
    @validator('chunk_size')
    def validate_chunk_size(cls, v):
        if v is not None and (v < 100 or v > 2000):
            raise ValueError('chunk_size must be between 100 and 2000')
        return v


class DocumentUpload(BaseModel):
    """Document upload request."""
    
    content: str = Field(..., description="Document content")
    title: Optional[str] = Field(None, description="Document title")
    source: Optional[str] = Field(None, description="Document source")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    @validator('content')
    def content_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Document content cannot be empty')
        return v.strip()
