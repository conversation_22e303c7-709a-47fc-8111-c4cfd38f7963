#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 sci-ag.py 脚本的基本功能
"""

import os
from dotenv import load_dotenv

def test_environment_variables():
    """测试环境变量是否正确加载"""
    print("🔍 测试环境变量...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    required_vars = ['DEEPSEEK_API_KEY', 'CORE_API_KEY', 'CORE_API_URL']
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"❌ {var}: 未设置")
    
    return all(os.getenv(var) for var in required_vars)

def test_imports():
    """测试关键模块导入"""
    print("\n🔍 测试模块导入...")
    
    try:
        from langchain_deepseek import ChatDeepSeek
        print("✅ langchain_deepseek 导入成功")
        
        from langgraph.graph import StateGraph
        print("✅ langgraph 导入成功")
        
        import pdfplumber
        print("✅ pdfplumber 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_deepseek_connection():
    """测试 DeepSeek 连接"""
    print("\n🔍 测试 DeepSeek 连接...")
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        llm = ChatDeepSeek(model="deepseek-chat", temperature=0.0)
        response = llm.invoke("Hello, this is a test. Please respond with 'Connection successful'.")
        
        print(f"✅ DeepSeek 响应: {response.content}")
        return True
    except Exception as e:
        print(f"❌ DeepSeek 连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 sci-ag.py 脚本...")
    
    # 测试环境变量
    env_ok = test_environment_variables()
    
    # 测试导入
    import_ok = test_imports()
    
    # 测试 DeepSeek 连接
    deepseek_ok = test_deepseek_connection()
    
    print("\n📊 测试结果总结:")
    print(f"环境变量: {'✅' if env_ok else '❌'}")
    print(f"模块导入: {'✅' if import_ok else '❌'}")
    print(f"DeepSeek连接: {'✅' if deepseek_ok else '❌'}")
    
    if all([env_ok, import_ok, deepseek_ok]):
        print("\n🎉 所有测试通过！sci-ag.py 脚本应该可以正常运行。")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    main()
