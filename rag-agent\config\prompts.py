"""Prompt templates for RAG Agent system."""

from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate


class PromptTemplates:
    """Collection of prompt templates used in the RAG system."""
    
    # RAG Generation Prompt
    RAG_GENERATION = """You are an assistant for question-answering tasks. 
Answer the question based only on the following context. 
Use three-to-five sentences maximum and keep the answer concise.

Context:
{context}

Question: {question}

Answer:"""
    
    # Document Relevance Grading Prompt
    RELEVANCE_GRADING = """You are a grader assessing relevance of a retrieved document to a user question.
If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant.
It does not need to be a stringent test. The goal is to filter out erroneous retrievals.
Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.

Retrieved document: 
{document}

User question: {question}"""
    
    # Hallucination Detection Prompt
    HALLUCINATION_DETECTION = """You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts.
Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts.

Set of facts:
{documents}

LLM generation: {generation}"""
    
    # Document Highlighting Prompt
    DOCUMENT_HIGHLIGHTING = """You are an advanced assistant for document search and retrieval. You are provided with the following:
1. A question.
2. A generated answer based on the question.
3. A set of documents that were referenced in generating the answer.

Your task is to identify and extract the exact inline segments from the provided documents that directly correspond to the content used to 
generate the given answer. The extracted segments must be verbatim snippets from the documents, ensuring a word-for-word match with the text 
in the provided documents.

Ensure that:
- (Important) Each segment is an exact match to a part of the document and is fully contained within the document text.
- The relevance of each segment to the generated answer is clear and directly supports the answer provided.
- (Important) If you didn't used the specific document don't mention it.

IMPORTANT: You must respond ONLY with valid JSON format. Do not include any thinking process, explanations, or additional text outside the JSON structure.

Used documents: 
{documents}

User question: {question}

Generated answer: {generation}

Remember: Respond ONLY with valid JSON. No additional text or thinking process."""
    
    # Answer Quality Assessment Prompt
    ANSWER_QUALITY = """You are an expert evaluator assessing the quality of an answer to a given question.
Evaluate the answer based on the following criteria:
1. Accuracy: Is the answer factually correct?
2. Completeness: Does the answer fully address the question?
3. Clarity: Is the answer clear and well-structured?
4. Relevance: Is the answer directly relevant to the question?

Question: {question}
Answer: {answer}
Context: {context}

Provide a score from 1-10 and brief explanation."""
    
    # Query Expansion Prompt
    QUERY_EXPANSION = """You are a query expansion expert. Given a user question, generate 3-5 alternative phrasings or related questions 
that could help retrieve more relevant documents. The expanded queries should capture different aspects or perspectives of the original question.

Original question: {question}

Generate alternative queries:"""
    
    # Summary Generation Prompt
    SUMMARY_GENERATION = """You are a document summarization expert. Create a concise summary of the following document that captures the main points 
and key information. The summary should be 2-3 sentences long.

Document: {document}

Summary:"""
    
    @classmethod
    def get_rag_generation_prompt(cls) -> ChatPromptTemplate:
        """Get RAG generation prompt template."""
        return ChatPromptTemplate.from_template(cls.RAG_GENERATION)
    
    @classmethod
    def get_relevance_grading_prompt(cls) -> ChatPromptTemplate:
        """Get relevance grading prompt template."""
        return ChatPromptTemplate.from_messages([
            ("system", "You are a grader assessing relevance of retrieved documents."),
            ("human", cls.RELEVANCE_GRADING)
        ])
    
    @classmethod
    def get_hallucination_detection_prompt(cls) -> ChatPromptTemplate:
        """Get hallucination detection prompt template."""
        return ChatPromptTemplate.from_messages([
            ("system", "You are a grader assessing whether an LLM generation is grounded in facts."),
            ("human", cls.HALLUCINATION_DETECTION)
        ])
    
    @classmethod
    def get_document_highlighting_prompt(cls) -> ChatPromptTemplate:
        """Get document highlighting prompt template."""
        return ChatPromptTemplate.from_template(cls.DOCUMENT_HIGHLIGHTING)
    
    @classmethod
    def get_answer_quality_prompt(cls) -> ChatPromptTemplate:
        """Get answer quality assessment prompt template."""
        return ChatPromptTemplate.from_template(cls.ANSWER_QUALITY)
    
    @classmethod
    def get_query_expansion_prompt(cls) -> ChatPromptTemplate:
        """Get query expansion prompt template."""
        return ChatPromptTemplate.from_template(cls.QUERY_EXPANSION)
    
    @classmethod
    def get_summary_generation_prompt(cls) -> ChatPromptTemplate:
        """Get summary generation prompt template."""
        return ChatPromptTemplate.from_template(cls.SUMMARY_GENERATION)
    
    @classmethod
    def get_custom_prompt(cls, template: str, **kwargs) -> ChatPromptTemplate:
        """Create a custom prompt template."""
        return ChatPromptTemplate.from_template(template.format(**kwargs))
