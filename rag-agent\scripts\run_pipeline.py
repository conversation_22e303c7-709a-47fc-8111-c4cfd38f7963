#!/usr/bin/env python3
"""Main script to run the complete RAG pipeline."""

import sys
import time
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from config.settings import get_settings
from src.core.document_loader import DocumentLoader
from src.core.embeddings import EmbeddingManager
from src.core.retriever import DocumentRetriever
from src.core.llm import LLMManager
from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


def test_system_components():
    """Test all system components."""
    logger.info("🧪 Testing system components...")
    
    # Test embedding model
    logger.info("Testing embedding model...")
    embedding_manager = EmbeddingManager()
    if not embedding_manager.test_embedding_model():
        logger.error("❌ Embedding model test failed")
        return False
    logger.info("✅ Embedding model test passed")
    
    # Test LLM
    logger.info("Testing LLM...")
    llm_manager = LLMManager()
    if not llm_manager.test_llm():
        logger.error("❌ LLM test failed")
        return False
    logger.info("✅ LLM test passed")
    
    return True


def load_and_index_documents():
    """Load and index documents into the vector store."""
    logger.info("📚 Loading and indexing documents...")
    
    settings = get_settings()
    
    # Initialize components
    document_loader = DocumentLoader()
    embedding_manager = EmbeddingManager()
    retriever = DocumentRetriever(embedding_manager)
    
    # Load documents from default URLs
    logger.info(f"Loading documents from {len(settings.default_urls)} URLs...")
    documents = document_loader.process_documents(
        sources=settings.default_urls,
        source_type="url"
    )
    
    if not documents:
        logger.error("❌ No documents were loaded")
        return False
    
    logger.info(f"✅ Loaded {len(documents)} document chunks")
    
    # Add documents to vector store
    logger.info("Adding documents to vector store...")
    doc_ids = retriever.add_documents(documents)
    
    logger.info(f"✅ Added {len(doc_ids)} documents to vector store")
    
    # Get collection info
    collection_info = retriever.get_collection_info()
    logger.info(f"📊 Vector store info: {collection_info}")
    
    return True


def run_sample_queries():
    """Run sample queries to test the system."""
    logger.info("🔍 Running sample queries...")
    
    # Initialize components
    embedding_manager = EmbeddingManager()
    retriever = DocumentRetriever(embedding_manager)
    llm_manager = LLMManager()
    
    # Sample questions
    questions = [
        "What are the different agentic design patterns?",
        "How does reflection work in AI agents?",
        "What is tool use in the context of AI agents?",
        "Explain multi-agent collaboration."
    ]
    
    for i, question in enumerate(questions, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"🔍 Question {i}: {question}")
        logger.info(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # Retrieve documents
            logger.info("Retrieving relevant documents...")
            documents = retriever.retrieve_documents(question)
            
            if not documents:
                logger.warning("No documents retrieved")
                continue
            
            logger.info(f"Retrieved {len(documents)} documents")
            
            # Format documents for context
            context = "\n\n".join([
                f"Document {j+1}:\nTitle: {doc.metadata.get('title', 'N/A')}\n"
                f"Source: {doc.metadata.get('source', 'N/A')}\n"
                f"Content: {doc.page_content}"
                for j, doc in enumerate(documents)
            ])
            
            # Generate answer
            logger.info("Generating answer...")
            prompt = f"""Answer the question based only on the following context:

{context}

Question: {question}

Answer:"""
            
            answer = llm_manager.generate_response(prompt)
            
            elapsed_time = time.time() - start_time
            
            # Display results
            logger.info(f"\n🤖 Answer:")
            logger.info(answer)
            logger.info(f"\n⏱️ Processing time: {elapsed_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"❌ Error processing question: {e}")
            continue


def main():
    """Main function."""
    logger.info("🚀 Starting RAG Agent Pipeline")
    logger.info("="*60)
    
    try:
        # Test system components
        if not test_system_components():
            logger.error("❌ System component tests failed")
            return 1
        
        # Load and index documents
        if not load_and_index_documents():
            logger.error("❌ Document loading and indexing failed")
            return 1
        
        # Run sample queries
        run_sample_queries()
        
        logger.info("\n" + "="*60)
        logger.info("🎉 RAG Agent Pipeline completed successfully!")
        logger.info("="*60)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Pipeline interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"💥 Pipeline failed with error: {e}")
        return 1


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    # Run main function
    exit_code = main()
    sys.exit(exit_code)
