"""Visualization utilities for RAPTOR Test system."""

from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from pathlib import Path

from src.utils.logger import get_logger

logger = get_logger(__name__)

# Try to import visualization libraries
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger.warning("Matplotlib/Seaborn not available. Visualization features disabled.")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logger.warning("Plotly not available. Interactive visualization features disabled.")


class TreeVisualizer:
    """Visualizer for RAPTOR tree structures."""
    
    def __init__(self):
        """Initialize the tree visualizer."""
        self.logger = get_logger(__name__)
    
    def plot_tree_structure(self, tree_data: Dict[str, Any], 
                           output_path: Optional[str] = None) -> bool:
        """Plot tree structure.
        
        Args:
            tree_data: Tree structure data
            output_path: Path to save the plot
            
        Returns:
            True if successful, False otherwise
        """
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("Matplotlib not available for tree visualization")
            return False
        
        try:
            # Create a simple tree visualization
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # For now, create a simple placeholder visualization
            levels = tree_data.get('max_level', 0) + 1
            nodes_per_level = [len([n for n in tree_data.get('nodes', {}).values() 
                                  if n.get('level') == i]) for i in range(levels)]
            
            ax.bar(range(levels), nodes_per_level)
            ax.set_xlabel('Tree Level')
            ax.set_ylabel('Number of Nodes')
            ax.set_title('RAPTOR Tree Structure')
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Tree visualization saved to {output_path}")
            else:
                plt.show()
            
            plt.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to plot tree structure: {e}")
            return False
    
    def plot_tree_metrics(self, metrics: Dict[str, Any], 
                         output_path: Optional[str] = None) -> bool:
        """Plot tree metrics.
        
        Args:
            metrics: Tree metrics data
            output_path: Path to save the plot
            
        Returns:
            True if successful, False otherwise
        """
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("Matplotlib not available for metrics visualization")
            return False
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # Plot basic metrics
            metric_names = list(metrics.keys())[:4]
            metric_values = [metrics[name] for name in metric_names]
            
            for i, (name, value) in enumerate(zip(metric_names, metric_values)):
                row, col = i // 2, i % 2
                axes[row, col].bar([name], [value])
                axes[row, col].set_title(f'{name}: {value}')
            
            plt.tight_layout()
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Tree metrics visualization saved to {output_path}")
            else:
                plt.show()
            
            plt.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to plot tree metrics: {e}")
            return False


class ClusterVisualizer:
    """Visualizer for clustering results."""
    
    def __init__(self):
        """Initialize the cluster visualizer."""
        self.logger = get_logger(__name__)
    
    def plot_clusters(self, embeddings: np.ndarray, 
                     labels: np.ndarray,
                     output_path: Optional[str] = None) -> bool:
        """Plot clustering results.
        
        Args:
            embeddings: Document embeddings
            labels: Cluster labels
            output_path: Path to save the plot
            
        Returns:
            True if successful, False otherwise
        """
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("Matplotlib not available for cluster visualization")
            return False
        
        try:
            # Use PCA for dimensionality reduction if needed
            from sklearn.decomposition import PCA
            
            if embeddings.shape[1] > 2:
                pca = PCA(n_components=2)
                embeddings_2d = pca.fit_transform(embeddings)
            else:
                embeddings_2d = embeddings
            
            # Create scatter plot
            plt.figure(figsize=(10, 8))
            scatter = plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], 
                                c=labels, cmap='tab10', alpha=0.7)
            plt.colorbar(scatter)
            plt.xlabel('First Principal Component')
            plt.ylabel('Second Principal Component')
            plt.title('Document Clustering Visualization')
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Cluster visualization saved to {output_path}")
            else:
                plt.show()
            
            plt.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to plot clusters: {e}")
            return False
    
    def plot_cluster_metrics(self, metrics: Dict[str, Any], 
                           output_path: Optional[str] = None) -> bool:
        """Plot clustering metrics.
        
        Args:
            metrics: Clustering metrics
            output_path: Path to save the plot
            
        Returns:
            True if successful, False otherwise
        """
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("Matplotlib not available for metrics visualization")
            return False
        
        try:
            # Create metrics visualization
            fig, ax = plt.subplots(figsize=(10, 6))
            
            metric_names = []
            metric_values = []
            
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    metric_names.append(key)
                    metric_values.append(value)
            
            if metric_names:
                bars = ax.bar(metric_names, metric_values)
                ax.set_title('Clustering Quality Metrics')
                ax.set_ylabel('Score')
                
                # Add value labels on bars
                for bar, value in zip(bars, metric_values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.3f}', ha='center', va='bottom')
                
                plt.xticks(rotation=45)
                plt.tight_layout()
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Cluster metrics visualization saved to {output_path}")
            else:
                plt.show()
            
            plt.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to plot cluster metrics: {e}")
            return False


def create_visualization_directory(base_path: str = "./plots") -> Path:
    """Create directory for visualizations.
    
    Args:
        base_path: Base path for visualization directory
        
    Returns:
        Path to visualization directory
    """
    viz_path = Path(base_path)
    viz_path.mkdir(parents=True, exist_ok=True)
    return viz_path


def is_visualization_available() -> Dict[str, bool]:
    """Check which visualization libraries are available.
    
    Returns:
        Dictionary indicating availability of visualization libraries
    """
    return {
        "matplotlib": MATPLOTLIB_AVAILABLE,
        "plotly": PLOTLY_AVAILABLE
    }
