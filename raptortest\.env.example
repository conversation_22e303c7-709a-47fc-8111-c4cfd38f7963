# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-embed-text
CHAT_MODEL=gemma3:12b

# Model Parameters
TEMPERATURE=0.1
MAX_TOKENS=2000
TOP_P=0.9

# RAPTOR Configuration
MAX_TREE_LEVELS=3
CLUSTERING_METHOD=gmm
MIN_CLUSTER_SIZE=2
MAX_CLUSTERS=10
CLUSTER_SELECTION_EPSILON=0.5

# Document Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=100
MAX_DOCUMENTS=1000

# Retrieval Settings
RETRIEVAL_K=5
SIMILARITY_THRESHOLD=0.7
SEARCH_TYPE=similarity
ENABLE_COMPRESSION=true
ENABLE_TREE_TRAVERSAL=true

# Vector Store Configuration
VECTORSTORE_TYPE=faiss
VECTORSTORE_PATH=./data/vectorstore
INDEX_NAME=raptor_index

# Tree Storage Configuration
TREE_STORAGE_PATH=./data/trees
TREE_FORMAT=json
ENABLE_TREE_CACHE=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/raptor.log
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Performance Settings
ENABLE_CACHING=true
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=5
BATCH_SIZE=32

# Evaluation Settings
ENABLE_EVALUATION=true
EVALUATION_METRICS=precision,recall,f1,bleu,rouge
GROUND_TRUTH_PATH=./data/evaluation/ground_truth.json

# Visualization Settings
ENABLE_VISUALIZATION=true
PLOT_DPI=300
FIGURE_SIZE_WIDTH=12
FIGURE_SIZE_HEIGHT=8

# API Configuration (if using API mode)
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Development Settings
DEBUG=false
ENABLE_PROFILING=false
SAVE_INTERMEDIATE_RESULTS=false
ENABLE_TREE_VALIDATION=true
