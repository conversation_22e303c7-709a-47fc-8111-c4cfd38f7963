
import os
from dotenv import load_dotenv

# Load environment variables from '.env' file
load_dotenv()

# 使用 Ollama 替代 Groq 和 Cohere
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import WebBaseLoader
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings, ChatOllama

# Set embeddings - 使用 Ollama 的嵌入模型
embedding_model = OllamaEmbeddings(
    model="nomic-embed-text",  # 推荐的嵌入模型
    base_url="http://localhost:11434"  # Ollama 默认地址
)

# 设置 LLM - 使用 Ollama 的聊天模型
llm = ChatOllama(
    model="qwen3:8b",  # 使用已安装的 qwen3:8b 模型
    base_url="http://localhost:11434",
    temperature=0.1
)

# Docs to index
urls = [
    "https://www.deeplearning.ai/the-batch/how-agents-can-improve-llm-performance/?ref=dl-staging-website.ghost.io",
    "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-2-reflection/?ref=dl-staging-website.ghost.io",
    "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-3-tool-use/?ref=dl-staging-website.ghost.io",
    "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-4-planning/?ref=dl-staging-website.ghost.io",
    "https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-5-multi-agent-collaboration/?ref=dl-staging-website.ghost.io"
]

# Load
docs = [WebBaseLoader(url).load() for url in urls]
docs_list = [item for sublist in docs for item in sublist]

# Split
text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
    chunk_size=500, chunk_overlap=0
)
doc_splits = text_splitter.split_documents(docs_list)

# Add to vectorstore
vectorstore = Chroma.from_documents(
    documents=doc_splits,
    collection_name="rag",
    embedding=embedding_model,
)

retriever = vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={'k': 4}, # number of documents to retrieve
            )

# 创建 RAG 链
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser

# 定义 RAG 提示模板
template = """Answer the question based only on the following context:
{context}

Question: {question}

Answer: """

prompt = ChatPromptTemplate.from_template(template)

# 创建 RAG 链
def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)

rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)

# 测试问题
question = "What are the different kind of agentic design patterns?"

print("🔍 Question:", question)
print("\n📚 Retrieved documents:")
docs = retriever.invoke(question)
for i, doc in enumerate(docs):
    print(f"\n{i+1}. Title: {doc.metadata.get('title', 'N/A')}")
    print(f"   Source: {doc.metadata.get('source', 'N/A')}")
    print(f"   Content preview: {doc.page_content[:200]}...")

print("\n🤖 LLM Answer:")
try:
    answer = rag_chain.invoke(question)
    print(answer)
except Exception as e:
    print(f"Error: {e}")
    print("请确保 Ollama 服务正在运行，并且已安装所需的模型:")
    print("- ollama pull nomic-embed-text")
    print("- ollama pull llama3.2")