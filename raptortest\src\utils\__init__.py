"""Utility functions for RAPTOR Test system."""

from .logger import get_logger, setup_logging
from .helpers import format_documents, clean_text, calculate_metrics

# Try to import visualization modules
try:
    from .visualization import TreeVisualizer, ClusterVisualizer
    VISUALIZATION_AVAILABLE = True
except ImportError:
    TreeVisualizer = None
    ClusterVisualizer = None
    VISUALIZATION_AVAILABLE = False

__all__ = [
    "get_logger",
    "setup_logging",
    "format_documents",
    "clean_text",
    "calculate_metrics",
    "TreeVisualizer",
    "ClusterVisualizer",
    "VISUALIZATION_AVAILABLE"
]
