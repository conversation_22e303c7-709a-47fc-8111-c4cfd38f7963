#!/usr/bin/env python3
"""Setup script for RAG Agent system."""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


def check_python_version():
    """Check if Python version is compatible."""
    logger.info("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8 or higher is required")
        return False
    
    logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def check_ollama_installation():
    """Check if Ollama is installed and running."""
    logger.info("🦙 Checking Ollama installation...")
    
    # Check if ollama command exists
    if not shutil.which("ollama"):
        logger.error("❌ Ollama is not installed or not in PATH")
        logger.info("Please install Ollama from: https://ollama.ai/")
        return False
    
    logger.info("✅ Ollama command found")
    
    # Check if Ollama service is running
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Ollama service is running")
            return True
        else:
            logger.warning("⚠️ Ollama service is not responding correctly")
            return False
    except Exception as e:
        logger.warning(f"⚠️ Cannot connect to Ollama service: {e}")
        logger.info("Please start Ollama service with: ollama serve")
        return False


def install_python_dependencies():
    """Install Python dependencies."""
    logger.info("📦 Installing Python dependencies...")
    
    requirements_file = Path(__file__).parent.parent / "requirements.txt"
    
    if not requirements_file.exists():
        logger.error("❌ requirements.txt not found")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True, capture_output=True, text=True)
        
        logger.info("✅ Python dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False


def install_ollama_models():
    """Install required Ollama models."""
    logger.info("🤖 Installing Ollama models...")
    
    models = [
        "nomic-embed-text",  # Embedding model
        "qwen3:8b"          # Chat model
    ]
    
    for model in models:
        logger.info(f"Installing model: {model}")
        try:
            result = subprocess.run([
                "ollama", "pull", model
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ Model {model} installed successfully")
            else:
                logger.error(f"❌ Failed to install model {model}: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ Timeout installing model {model}")
            return False
        except Exception as e:
            logger.error(f"❌ Error installing model {model}: {e}")
            return False
    
    return True


def create_directories():
    """Create necessary directories."""
    logger.info("📁 Creating directories...")
    
    base_path = Path(__file__).parent.parent
    directories = [
        "data/documents",
        "data/vectorstore", 
        "logs",
        "tests/test_core",
        "tests/test_services",
        "tests/test_integration"
    ]
    
    for directory in directories:
        dir_path = base_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Created directory: {dir_path}")
    
    logger.info("✅ Directories created successfully")
    return True


def setup_environment():
    """Setup environment configuration."""
    logger.info("⚙️ Setting up environment...")
    
    base_path = Path(__file__).parent.parent
    env_example = base_path / ".env.example"
    env_file = base_path / ".env"
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        logger.info("✅ Created .env file from .env.example")
        logger.info("📝 Please review and update .env file with your settings")
    elif env_file.exists():
        logger.info("✅ .env file already exists")
    else:
        logger.warning("⚠️ No .env.example file found")
    
    return True


def test_installation():
    """Test the installation."""
    logger.info("🧪 Testing installation...")
    
    try:
        # Test imports
        from config.settings import get_settings
        from src.core.embeddings import EmbeddingManager
        from src.core.llm import LLMManager
        
        logger.info("✅ All imports successful")
        
        # Test embedding model
        embedding_manager = EmbeddingManager()
        if embedding_manager.test_embedding_model():
            logger.info("✅ Embedding model test passed")
        else:
            logger.error("❌ Embedding model test failed")
            return False
        
        # Test LLM
        llm_manager = LLMManager()
        if llm_manager.test_llm():
            logger.info("✅ LLM test passed")
        else:
            logger.error("❌ LLM test failed")
            return False
        
        logger.info("✅ Installation test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Installation test failed: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 Starting RAG Agent Setup")
    logger.info("="*60)
    
    setup_steps = [
        ("Checking Python version", check_python_version),
        ("Checking Ollama installation", check_ollama_installation),
        ("Installing Python dependencies", install_python_dependencies),
        ("Installing Ollama models", install_ollama_models),
        ("Creating directories", create_directories),
        ("Setting up environment", setup_environment),
        ("Testing installation", test_installation),
    ]
    
    for step_name, step_func in setup_steps:
        logger.info(f"\n📋 {step_name}...")
        try:
            if not step_func():
                logger.error(f"❌ Setup failed at step: {step_name}")
                return 1
        except Exception as e:
            logger.error(f"❌ Setup failed at step '{step_name}': {e}")
            return 1
    
    logger.info("\n" + "="*60)
    logger.info("🎉 RAG Agent setup completed successfully!")
    logger.info("="*60)
    logger.info("\n📝 Next steps:")
    logger.info("1. Review and update the .env file with your settings")
    logger.info("2. Run the pipeline: python scripts/run_pipeline.py")
    logger.info("3. Check the logs directory for detailed logs")
    
    return 0


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    # Run setup
    exit_code = main()
    sys.exit(exit_code)
