#!/usr/bin/env python3
"""Complete demo script for RAPTOR Test system."""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.services.raptor_service import RaptorService
from src.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


def create_sample_documents():
    """Create sample documents for demonstration."""
    return [
        """一、题释
阴：此非阴阳之阴。阴为隐，为伏藏，无形玄冥之象，与天同，阴即天道。符：符即应，为动静，为显眀，符应显化之象。阴符者，天道幽玄，符应昭昭。

二、上篇

观天之道，执天之行，尽矣。
【符字】
1、观：观的古符字为一鸟形之目视状。此鸟形当爲鴞类，其目大能夜视，可于暗中观物。故观的古符意为能视幽暗之物，明察玄冥之象。
2、执：执的古符字为人之双手持一上下相应之物，或上下相应之物落于人之双手中。上下箭头者爲宇，中间菱形者爲宙，宇宙在乎手即爲执。故执有二意，得之符应，落于身行。
【揆度】
阴道无形，天道幽玄，能观之而不可见之，故曰观天之道。天道符应，万物显明，人道居中，得而躬行，故曰执天之性。天道幽隐，天行显化，隐显皆备，一体俱显。故曰尽矣，极矣。

故天有五贼，见之者昌。
【符字】
1、贼：贼的古符字为人+贝+戈，戈为天道，贝为测度物之价值，故贼的古符意爲人测度天道。天道为一，测度之则分之，如混沌琢七窍。左传曰：毁则爲贼。
【揆度】
天道观之，可分爲五，即先天五数，一二三四五。先天数爲体，为生数，其用则显化为后天数六七八九十。后天数为用，为成数。见通于显，先天数显化为后天数，则无形化有形，万物昌明而显。
此为河洛之源，九宫之设。隐显之道，伏藏之法。
观天之道，则天有五贼；执天之行，则见之者昌。此为道行一词之源。
（天有五贼，地有五行，人居其中，化而为洛书九宫。五贼为隐，五行爲显。此也可备一说）

五贼在心，施行于天。
【符字】
施：施的古符字为物成而敷布四方。
【揆度】
三才之道，人居于中；隐显之间，人为中枢；九宫之中，五为环中。
观天之道，天有五贼，皆爲人心所显，故曰五贼在心，心者，人心。
执天之行，爲人之行；见之者昌为人行之施。
故天道天行，隐显之道皆不离人道。得道施行皆归于己。

宇宙在乎手，万化生乎身。
【揆度】
此进一步讲了施行之显。宇宙者，时空。宇宙在乎手，为执之象，人执天之行，则宇宙生矣。万化生乎身，为昌之象；身爲己，人能执天之行，则天道显化，万物昌明。

天性，人也；人心，机也。
【符字】
1、性：性爲心的生发处，心为本，性爲用。（性爲本，情爲用；情爲本，欲爲用，性情不离于心，欲则失心）
2、机：机的古符字为人+丝+戈，戈为天道，丝为万化，人居其中。故机之符意爲人为天道显化万物之中枢。
【揆度】
人为天地之中，天道发用于人，故曰天性，人也。人执天之行，则万化显眀，故曰人心，机也。机者，三才之中枢。

立天之道，以定人也。
【揆度】
人观天道，执天行，为往道；立天之道，以定人也，为复道。往而复之，周流之道备矣。
 

天发杀机，移星易宿；地发杀机，龙蛇起陆；人发杀机，天地反覆。天人合发，万变定基。
【揆度】
天发杀机，地发杀机，人发杀机，皆立于一极，居二相待而发，发则爲动。天人合发，为三才贯通。和而一体，居一不二；发而无发，往复周流。发而能静。此往复动静之道。

性有巧拙，可以伏藏。
【符字】
巧，拙：巧的古符字有下行，探索钻研，尖实之象。拙的古符字为上行，虚含，包容之象，巧拙相对而生。
【揆度】
此段可取天、人两象。
若性爲天性，则性有巧拙，可理解爲天性有虚实，如九宫之虚实性，一二三四为虚性，六七八九为实性。
可以伏藏者，巧入于拙为伏，拙含于巧为藏，巧拙为二，伏藏则复归于一。一九宫相对相成，一为虚为拙，九为巧为实，伏藏者，九伏于一，一藏于九。一九分之爲二，合之爲一。伏藏为之体，巧拙为之用。其他对宫亦然。此天之伏藏之法。
若性爲人，巧，可取象为人之有爲；拙可取象为人之无爲。有爲者实，无爲者虚。为而无爲者，谓之伏，无爲而为者，谓之藏。此人之伏藏之法。
性有巧拙，为二，为用；可以伏藏，为一，为体。
此亦禅家，三关之说，死去活来之言，杀人剑活人刀之法。

九窍之邪，在乎三要，可以动静。
【符字】
邪：邪的古符字为牙+邑。符意爲交错聚集之。
【揆度】
九窍者，在天言九宫，在人言九窍。三要者，天地人。邪者，相待交错之实。"""
    ]


def demonstrate_tree_construction(raptor: RaptorService):
    """Demonstrate tree construction functionality."""
    print("\n🌳 Tree Construction Demo")
    print("=" * 50)
    
    # Create sample documents
    documents = create_sample_documents()
    document_ids = [f"env_doc_{i+1}" for i in range(len(documents))]
    
    print(f"📚 Building RAPTOR tree from {len(documents)} environmental documents...")
    
    # Build tree
    tree = raptor.build_tree_from_documents(
        documents=documents,
        document_ids=document_ids,
        tree_id="environmental_tree"
    )
    
    # Get tree statistics
    stats = raptor.get_tree_statistics("environmental_tree")
    
    print(f"✅ Tree construction completed!")
    print(f"   📊 Statistics:")
    print(f"      Total nodes: {stats['total_nodes']}")
    print(f"      Leaf nodes: {stats['total_leaves']}")
    print(f"      Tree levels: {stats['max_level'] + 1}")
    print(f"      Average cluster size: {stats['avg_cluster_size']:.2f}")
    print(f"      Average tree depth: {stats['avg_tree_depth']:.2f}")
    
    # Show nodes per level
    print(f"   📈 Nodes per level:")
    for level, count in stats['nodes_per_level'].items():
        print(f"      Level {level}: {count} nodes")
    
    return tree


def demonstrate_retrieval_strategies(raptor: RaptorService):
    """Demonstrate different retrieval strategies."""
    print("\n🔍 Retrieval Strategies Demo")
    print("=" * 50)
    
    # Test queries
    queries = [
        "天有五贼是什么意思?",
        
    ]
    
    strategies = ["tree_traversal", "flat_retrieval", "level_based", "hybrid"]
    
    for query in queries[:2]:  # Test first 2 queries to save time
        print(f"\n🔍 Query: {query}")
        print("-" * 40)
        
        for strategy in strategies:
            try:
                result = raptor.query_tree(
                    tree_id="environmental_tree",
                    query=query,
                    k=3,
                    strategy=strategy,
                    generate_answer=False  # Skip answer generation for speed
                )
                
                metrics = result['metrics']
                print(f"   {strategy:15} | "
                      f"Retrieved: {metrics['nodes_retrieved']:2d} | "
                      f"Time: {metrics['retrieval_time']:.3f}s")
                
            except Exception as e:
                print(f"   {strategy:15} | Error: {e}")


def demonstrate_answer_generation(raptor: RaptorService):
    """Demonstrate answer generation functionality."""
    print("\n🤖 Answer Generation Demo")
    print("=" * 50)
    
    queries = [
        "讲讲阴符经?",
        
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n📝 Question {i}: {query}")
        print("-" * 60)
        
        try:
            result = raptor.query_tree(
                tree_id="environmental_tree",
                query=query,
                k=4,
                strategy="tree_traversal",
                generate_answer=True
            )
            
            print(f"🤖 Answer: {result['answer']}")
            print(f"📊 Metrics:")
            print(f"   Retrieval time: {result['metrics']['retrieval_time']:.2f}s")
            print(f"   Generation time: {result['metrics']['generation_time']:.2f}s")
            print(f"   Total time: {result['metrics']['total_time']:.2f}s")
            print(f"   Nodes used: {result['metrics']['nodes_retrieved']}")
            
            # Show source information
            print(f"📚 Sources used:")
            for j, node in enumerate(result['retrieved_nodes'][:3], 1):
                content_preview = node['content'][:100] + "..." if len(node['content']) > 100 else node['content']
                print(f"   {j}. Level {node['level']}: {content_preview}")
            
        except Exception as e:
            print(f"❌ Error: {e}")


def demonstrate_tree_persistence(raptor: RaptorService):
    """Demonstrate tree saving and loading."""
    print("\n💾 Tree Persistence Demo")
    print("=" * 50)
    
    # Save tree
    save_path = "./data/trees/environmental_tree.json"
    print(f"💾 Saving tree to {save_path}...")
    
    if raptor.save_tree("environmental_tree", save_path):
        print("✅ Tree saved successfully!")
        
        # Delete from memory
        raptor.delete_tree("environmental_tree")
        print("🗑️ Tree deleted from memory")
        
        # Load back
        print(f"📂 Loading tree from {save_path}...")
        if raptor.load_tree("environmental_tree_loaded", save_path):
            print("✅ Tree loaded successfully!")
            
            # Verify it works
            result = raptor.query_tree(
                tree_id="environmental_tree_loaded",
                query="What is renewable energy?",
                k=2,
                generate_answer=False
            )
            print(f"✅ Verification query successful - retrieved {len(result['retrieved_nodes'])} nodes")
        else:
            print("❌ Failed to load tree")
    else:
        print("❌ Failed to save tree")


def main():
    """Main demo function."""
    print("🚀 RAPTOR Test Complete Demo")
    print("=" * 60)
    
    # Initialize RAPTOR service
    print("🔧 Initializing RAPTOR service...")
    raptor = RaptorService()
    
    # Test system components
    print("🧪 Testing system components...")
    if not raptor.test_system_components():
        print("❌ System component tests failed")
        return False
    print("✅ All system components working!")
    
    try:
        # Demonstrate different features
        tree = demonstrate_tree_construction(raptor)
        demonstrate_retrieval_strategies(raptor)
        demonstrate_answer_generation(raptor)
        demonstrate_tree_persistence(raptor)
        
        # Show system status
        print("\n📊 System Status")
        print("=" * 50)
        status = raptor.get_system_status()
        print(f"Embedding model: {status['embedding_model_status']}")
        print(f"Summarization model: {status['summarization_model_status']}")
        print(f"Loaded trees: {status['loaded_trees']}")
        
        print("\n" + "=" * 60)
        print("🎉 RAPTOR Test Demo completed successfully!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")
        return False


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")
        sys.exit(1)
