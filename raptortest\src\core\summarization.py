"""Summarization functionality for RAPTOR system."""

from typing import List, Optional, Dict, Any
import logging
import time

from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import get_settings
from config.prompts import PromptTemplates
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SummarizationManager:
    """Manages document summarization for RAPTOR tree construction."""
    
    def __init__(self):
        """Initialize the summarization manager."""
        self.settings = get_settings()
        self.prompts = PromptTemplates()
        self._llm = None
        
    @property
    def llm(self) -> ChatOllama:
        """Get or create the LLM instance."""
        if self._llm is None:
            logger.info(f"Initializing LLM: {self.settings.chat_model}")
            try:
                self._llm = ChatOllama(
                    model=self.settings.chat_model,
                    base_url=self.settings.ollama_base_url,
                    temperature=self.settings.temperature,
                )
                logger.info("LLM initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize LLM: {e}")
                raise
        return self._llm
    
    def summarize_text(self, text: str) -> str:
        """Summarize a single text.
        
        Args:
            text: Text to summarize
            
        Returns:
            Summary text
        """
        logger.debug(f"Summarizing text of length {len(text)}")
        
        try:
            prompt = self.prompts.get_summarization_prompt()
            messages = prompt.format_messages(text=text)
            
            response = self.llm.invoke(messages)
            summary = response.content.strip()
            
            logger.debug(f"Generated summary of length {len(summary)}")
            return summary
            
        except Exception as e:
            logger.error(f"Failed to summarize text: {e}")
            return text  # Return original text if summarization fails
    
    def summarize_cluster(self, documents: List[str], 
                         cluster_id: int, 
                         level: int) -> str:
        """Summarize a cluster of documents.
        
        Args:
            documents: List of document texts in the cluster
            cluster_id: Cluster identifier
            level: Tree level
            
        Returns:
            Cluster summary
        """
        logger.info(f"Summarizing cluster {cluster_id} at level {level} with {len(documents)} documents")
        start_time = time.time()
        
        try:
            # Combine documents with separators
            combined_text = "\n\n---\n\n".join(documents)
            
            # Use cluster-specific prompt
            prompt = self.prompts.get_tree_level_summary_prompt()
            messages = prompt.format_messages(
                documents=combined_text,
                level=level
            )
            
            response = self.llm.invoke(messages)
            summary = response.content.strip()
            
            elapsed_time = time.time() - start_time
            logger.info(f"Generated cluster summary in {elapsed_time:.2f} seconds")
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to summarize cluster: {e}")
            # Fallback: return first document or truncated combination
            return self._fallback_summary(documents)
    
    def batch_summarize(self, texts: List[str], 
                       batch_size: Optional[int] = None) -> List[str]:
        """Summarize multiple texts in batches.
        
        Args:
            texts: List of texts to summarize
            batch_size: Size of each batch (uses settings default if None)
            
        Returns:
            List of summaries
        """
        if batch_size is None:
            batch_size = self.settings.batch_size
            
        logger.info(f"Batch summarizing {len(texts)} texts in batches of {batch_size}")
        
        summaries = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_num = i // batch_size + 1
            batch_texts = texts[i:i + batch_size]
            
            logger.debug(f"Processing batch {batch_num}/{total_batches}")
            
            try:
                batch_summaries = [self.summarize_text(text) for text in batch_texts]
                summaries.extend(batch_summaries)
                
                # Add delay between batches
                if batch_num < total_batches:
                    time.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"Failed to process batch {batch_num}: {e}")
                # Add original texts as fallback
                summaries.extend(batch_texts)
        
        logger.info("Batch summarization completed")
        return summaries
    
    def adaptive_summarize(self, text: str, 
                          target_length: Optional[int] = None) -> str:
        """Adaptively summarize text based on length.
        
        Args:
            text: Text to summarize
            target_length: Target summary length (auto-determined if None)
            
        Returns:
            Summary text
        """
        if target_length is None:
            # Adaptive target length based on original text length
            text_length = len(text)
            if text_length < 500:
                return text  # Don't summarize short texts
            elif text_length < 2000:
                target_length = text_length // 2
            else:
                target_length = text_length // 3
        
        logger.debug(f"Adaptive summarization: {len(text)} -> ~{target_length} characters")
        
        try:
            # Create adaptive prompt
            adaptive_prompt = f"""Summarize the following text to approximately {target_length} characters.
Focus on the most important information and key points.

Text:
{text}

Summary (~{target_length} characters):"""
            
            messages = [HumanMessage(content=adaptive_prompt)]
            response = self.llm.invoke(messages)
            summary = response.content.strip()
            
            return summary
            
        except Exception as e:
            logger.error(f"Adaptive summarization failed: {e}")
            return self._truncate_text(text, target_length)
    
    def hierarchical_summarize(self, documents: List[str], 
                             metadata: Dict[str, Any]) -> str:
        """Perform hierarchical summarization with context awareness.
        
        Args:
            documents: List of documents to summarize
            metadata: Metadata about the documents and context
            
        Returns:
            Hierarchical summary
        """
        logger.info("Performing hierarchical summarization")
        
        try:
            level = metadata.get('level', 0)
            parent_summary = metadata.get('parent_summary', '')
            
            # Create context-aware prompt
            context_prompt = f"""You are creating a hierarchical summary at level {level}.
            
Parent context: {parent_summary}

Create a summary that:
1. Builds upon the parent context
2. Captures the essence of the documents
3. Maintains hierarchical coherence
4. Preserves important details

Documents to summarize:
{chr(10).join(f"Document {i+1}: {doc}" for i, doc in enumerate(documents))}

Hierarchical Summary:"""
            
            messages = [HumanMessage(content=context_prompt)]
            response = self.llm.invoke(messages)
            summary = response.content.strip()
            
            return summary
            
        except Exception as e:
            logger.error(f"Hierarchical summarization failed: {e}")
            return self._fallback_summary(documents)
    
    def validate_summary(self, original_text: str, summary: str) -> Dict[str, Any]:
        """Validate summary quality.
        
        Args:
            original_text: Original text
            summary: Generated summary
            
        Returns:
            Validation metrics
        """
        try:
            compression_ratio = len(summary) / len(original_text) if original_text else 0
            
            # Simple keyword preservation check
            original_words = set(original_text.lower().split())
            summary_words = set(summary.lower().split())
            keyword_preservation = len(summary_words.intersection(original_words)) / len(original_words) if original_words else 0
            
            return {
                "compression_ratio": compression_ratio,
                "keyword_preservation": keyword_preservation,
                "original_length": len(original_text),
                "summary_length": len(summary),
                "is_valid": 0.1 <= compression_ratio <= 0.8 and keyword_preservation >= 0.3
            }
            
        except Exception as e:
            logger.error(f"Summary validation failed: {e}")
            return {"is_valid": False}
    
    def _fallback_summary(self, documents: List[str]) -> str:
        """Create fallback summary when LLM summarization fails.
        
        Args:
            documents: List of documents
            
        Returns:
            Fallback summary
        """
        if not documents:
            return ""
        
        # Simple fallback: take first document or truncate combined text
        if len(documents) == 1:
            return self._truncate_text(documents[0], 1000)
        else:
            combined = " ".join(documents)
            return self._truncate_text(combined, 1500)
    
    def _truncate_text(self, text: str, max_length: int) -> str:
        """Truncate text to maximum length.
        
        Args:
            text: Text to truncate
            max_length: Maximum length
            
        Returns:
            Truncated text
        """
        if len(text) <= max_length:
            return text
        
        # Try to truncate at sentence boundary
        truncated = text[:max_length]
        last_period = truncated.rfind('.')
        last_space = truncated.rfind(' ')
        
        if last_period > max_length * 0.8:
            return truncated[:last_period + 1]
        elif last_space > max_length * 0.8:
            return truncated[:last_space]
        else:
            return truncated + "..."
    
    def test_summarization(self) -> bool:
        """Test if summarization is working correctly.
        
        Returns:
            True if working, False otherwise
        """
        logger.info("Testing summarization")
        
        try:
            test_text = """This is a test document for summarization. 
            It contains multiple sentences and should be summarized effectively. 
            The summarization system should capture the main points while reducing length."""
            
            summary = self.summarize_text(test_text)
            
            if summary and len(summary) > 0:
                logger.info(f"Summarization test successful. Summary length: {len(summary)}, Original length: {len(test_text)}")
                return True
            else:
                logger.error("Summarization test failed: empty summary")
                return False
                
        except Exception as e:
            logger.error(f"Summarization test failed: {e}")
            return False
