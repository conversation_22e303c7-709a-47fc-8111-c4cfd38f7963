# RAPTOR Test - Recursive Abstractive Processing for Tree-Organized Retrieval

一个基于 Ollama 的 RAPTOR 系统实现，用于构建层次化文档树并进行高级检索增强生成。

## 🚀 功能特性

- **层次化文档树构建**: 通过递归聚类和摘要构建多层次文档结构
- **高斯混合模型聚类**: 使用 GMM 进行智能文档聚类
- **树遍历检索**: 支持从顶层到底层的层次化检索
- **上下文压缩**: 智能提取相关信息，减少噪音
- **可视化分析**: 提供聚类和树结构的可视化
- **本地化部署**: 使用 Ollama 实现完全本地化运行
- **性能评估**: 内置评估指标和对比分析

## 📁 项目结构

```
raptortest/
├── config/                 # 配置文件
│   ├── __init__.py
│   ├── settings.py         # 主配置文件
│   └── prompts.py          # 提示词模板
├── src/                    # 源代码
│   ├── __init__.py
│   ├── core/               # 核心功能
│   │   ├── __init__.py
│   │   ├── embeddings.py   # 嵌入管理
│   │   ├── clustering.py   # 聚类算法
│   │   ├── summarization.py # 摘要生成
│   │   ├── tree_builder.py # 树构建
│   │   └── retrieval.py    # 检索系统
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── schemas.py      # Pydantic 模型
│   │   └── tree_node.py    # 树节点模型
│   ├── services/           # 业务逻辑
│   │   ├── __init__.py
│   │   ├── raptor_service.py # 主要 RAPTOR 服务
│   │   ├── document_service.py # 文档处理服务
│   │   └── query_service.py    # 查询处理服务
│   ├── utils/              # 工具函数
│   │   ├── __init__.py
│   │   ├── logger.py       # 日志管理
│   │   ├── visualization.py # 可视化工具
│   │   └── helpers.py      # 辅助函数
│   └── evaluation/         # 评估模块
│       ├── __init__.py
│       ├── metrics.py      # 评估指标
│       └── evaluator.py    # 评估器
├── data/                   # 数据目录
│   ├── documents/          # 文档存储
│   ├── trees/              # 树结构存储
│   └── vectorstore/        # 向量数据库
├── scripts/                # 脚本文件
│   ├── setup.py           # 环境设置
│   ├── build_tree.py      # 构建树脚本
│   └── run_evaluation.py  # 运行评估
├── tests/                  # 测试文件
│   ├── __init__.py
│   ├── test_core/
│   ├── test_services/
│   └── test_integration/
├── notebooks/              # Jupyter 笔记本
│   ├── demo.ipynb         # 演示笔记本
│   └── analysis.ipynb     # 分析笔记本
├── requirements.txt        # 依赖包
├── .env.example           # 环境变量示例
├── .gitignore             # Git 忽略文件
├── docker-compose.yml     # Docker 配置
├── Dockerfile             # Docker 镜像
└── demo.py                # 演示脚本
```

## 🛠️ 安装和设置

### 1. 环境要求

- Python 3.8+
- Ollama
- Git

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置 Ollama

```bash
# 安装所需模型
ollama pull nomic-embed-text
ollama pull gemma3:12b

# 启动 Ollama 服务
ollama serve
```

### 4. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置文件
nano .env
```

## 🚀 快速开始

### 基本使用

```python
from src.services.raptor_service import RaptorService

# 初始化 RAPTOR 系统
raptor = RaptorService()

# 构建文档树
tree = raptor.build_tree_from_documents(documents)

# 查询
result = raptor.query("What is the greenhouse effect?")
print(result.answer)
```

### 运行演示

```bash
python demo.py
```

### 构建文档树

```bash
python scripts/build_tree.py --input data/documents/ --output data/trees/
```

## 📊 RAPTOR 系统组件

### 1. 文档树构建
- 递归聚类和摘要
- 多层次结构组织
- 元数据管理

### 2. 聚类算法
- 高斯混合模型 (GMM)
- 自适应聚类数量
- 聚类质量评估

### 3. 摘要生成
- 基于 LLM 的智能摘要
- 上下文感知摘要
- 层次化摘要策略

### 4. 检索系统
- 树遍历检索
- 层次化检索
- 上下文压缩检索

### 5. 可视化分析
- 聚类可视化
- 树结构可视化
- 性能分析图表

## 🔧 配置选项

主要配置在 `config/settings.py` 中：

```python
# Ollama 配置
OLLAMA_BASE_URL = "http://localhost:11434"
EMBEDDING_MODEL = "nomic-embed-text"
CHAT_MODEL = "gemma3:12b"

# RAPTOR 配置
MAX_TREE_LEVELS = 3
CLUSTERING_METHOD = "gmm"
MIN_CLUSTER_SIZE = 2
MAX_CLUSTERS = 10

# 检索配置
RETRIEVAL_K = 5
COMPRESSION_ENABLED = True
TREE_TRAVERSAL_ENABLED = True
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_services/test_raptor_service.py
```

## 📈 性能评估

系统包含完整的评估框架：

- 检索质量评估
- 生成质量评估
- 树结构质量评估
- 性能基准测试

```bash
python scripts/run_evaluation.py
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系维护者。
