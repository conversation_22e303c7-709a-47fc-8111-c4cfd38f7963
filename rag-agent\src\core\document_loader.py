"""Document loading and processing functionality."""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path
import logging

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import WebBase<PERSON>oader, TextLoader, PyPDFLoader
from langchain_core.documents import Document

from config.settings import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DocumentLoader:
    """Handles loading and processing of documents from various sources."""
    
    def __init__(self):
        """Initialize the document loader."""
        self.settings = get_settings()
        self.text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=self.settings.chunk_size,
            chunk_overlap=self.settings.chunk_overlap
        )
        
    def load_from_urls(self, urls: List[str]) -> List[Document]:
        """Load documents from a list of URLs.
        
        Args:
            urls: List of URLs to load documents from
            
        Returns:
            List of loaded documents
        """
        logger.info(f"Loading documents from {len(urls)} URLs")
        documents = []
        
        for url in urls:
            try:
                logger.debug(f"Loading document from URL: {url}")
                loader = WebBaseLoader(url)
                docs = loader.load()
                documents.extend(docs)
                logger.debug(f"Successfully loaded {len(docs)} documents from {url}")
            except Exception as e:
                logger.error(f"Failed to load document from {url}: {e}")
                continue
                
        logger.info(f"Successfully loaded {len(documents)} documents from URLs")
        return documents
    
    def load_from_files(self, file_paths: List[str]) -> List[Document]:
        """Load documents from local files.
        
        Args:
            file_paths: List of file paths to load documents from
            
        Returns:
            List of loaded documents
        """
        logger.info(f"Loading documents from {len(file_paths)} files")
        documents = []
        
        for file_path in file_paths:
            try:
                path = Path(file_path)
                if not path.exists():
                    logger.warning(f"File not found: {file_path}")
                    continue
                    
                logger.debug(f"Loading document from file: {file_path}")
                
                if path.suffix.lower() == '.pdf':
                    loader = PyPDFLoader(file_path)
                elif path.suffix.lower() in ['.txt', '.md']:
                    loader = TextLoader(file_path, encoding='utf-8')
                else:
                    logger.warning(f"Unsupported file type: {path.suffix}")
                    continue
                    
                docs = loader.load()
                documents.extend(docs)
                logger.debug(f"Successfully loaded {len(docs)} documents from {file_path}")
                
            except Exception as e:
                logger.error(f"Failed to load document from {file_path}: {e}")
                continue
                
        logger.info(f"Successfully loaded {len(documents)} documents from files")
        return documents
    
    def load_from_directory(self, directory_path: str, 
                          file_extensions: Optional[List[str]] = None) -> List[Document]:
        """Load all documents from a directory.
        
        Args:
            directory_path: Path to the directory
            file_extensions: List of file extensions to include (e.g., ['.txt', '.pdf'])
            
        Returns:
            List of loaded documents
        """
        if file_extensions is None:
            file_extensions = ['.txt', '.md', '.pdf']
            
        directory = Path(directory_path)
        if not directory.exists():
            logger.error(f"Directory not found: {directory_path}")
            return []
            
        file_paths = []
        for ext in file_extensions:
            file_paths.extend(directory.glob(f"**/*{ext}"))
            
        return self.load_from_files([str(path) for path in file_paths])
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """Split documents into smaller chunks.
        
        Args:
            documents: List of documents to split
            
        Returns:
            List of document chunks
        """
        logger.info(f"Splitting {len(documents)} documents into chunks")
        
        try:
            doc_splits = self.text_splitter.split_documents(documents)
            logger.info(f"Successfully split documents into {len(doc_splits)} chunks")
            return doc_splits
        except Exception as e:
            logger.error(f"Failed to split documents: {e}")
            return documents
    
    def process_documents(self, sources: List[str], 
                         source_type: str = "url") -> List[Document]:
        """Process documents from various sources.
        
        Args:
            sources: List of sources (URLs or file paths)
            source_type: Type of source ("url", "file", or "directory")
            
        Returns:
            List of processed document chunks
        """
        logger.info(f"Processing documents from {source_type} sources")
        
        # Load documents
        if source_type == "url":
            documents = self.load_from_urls(sources)
        elif source_type == "file":
            documents = self.load_from_files(sources)
        elif source_type == "directory":
            documents = []
            for directory in sources:
                documents.extend(self.load_from_directory(directory))
        else:
            raise ValueError(f"Unsupported source type: {source_type}")
        
        if not documents:
            logger.warning("No documents were loaded")
            return []
        
        # Split documents
        doc_splits = self.split_documents(documents)
        
        # Apply document limit
        if len(doc_splits) > self.settings.max_documents:
            logger.warning(f"Limiting documents to {self.settings.max_documents} chunks")
            doc_splits = doc_splits[:self.settings.max_documents]
        
        logger.info(f"Successfully processed {len(doc_splits)} document chunks")
        return doc_splits
    
    def get_document_metadata(self, document: Document) -> Dict[str, Any]:
        """Extract metadata from a document.
        
        Args:
            document: Document to extract metadata from
            
        Returns:
            Dictionary containing document metadata
        """
        metadata = document.metadata.copy()
        metadata.update({
            'content_length': len(document.page_content),
            'word_count': len(document.page_content.split()),
            'char_count': len(document.page_content)
        })
        return metadata
