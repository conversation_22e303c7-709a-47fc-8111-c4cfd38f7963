2025-07-12 14:46:18,144 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:46:18,145 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:46:19,176 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:46:21,442 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:46:21,442 - src.core.embeddings - INFO - Generating embeddings for 3 documents
2025-07-12 14:46:21,517 - src.core.embeddings - INFO - Generated embeddings in 0.08 seconds
2025-07-12 14:46:25,738 - src.core.clustering - INFO - Optimal number of clusters: 2 (score: -1.0000)
2025-07-12 14:46:25,742 - src.core.clustering - INFO - Performing gmm clustering with 2 clusters
2025-07-12 14:46:25,916 - src.core.clustering - INFO - Clustering completed successfully with 2 clusters
2025-07-12 14:46:25,916 - src.core.clustering - INFO - Clustering validation passed
2025-07-12 14:46:25,916 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:46:25,917 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:46:26,951 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:46:50,323 - src.core.summarization - INFO - Summarization test successful
2025-07-12 14:56:00,375 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 14:56:01,555 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:56:01,555 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:56:02,596 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:56:03,591 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:56:03,591 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 14:56:03,591 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 14:56:03,591 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:56:03,591 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:56:04,627 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:56:26,658 - src.core.summarization - INFO - Summarization test successful
2025-07-12 14:56:26,659 - src.services.raptor_service - INFO - ✅ Summarization Model test passed
2025-07-12 14:56:26,659 - src.services.raptor_service - INFO - Building RAPTOR tree from 8 documents
2025-07-12 14:56:26,659 - src.core.tree_builder - INFO - Building RAPTOR tree from 8 documents
2025-07-12 14:56:26,660 - src.core.tree_builder - INFO - Building level 1 with 8 nodes
2025-07-12 14:56:26,660 - src.core.embeddings - INFO - Generating embeddings for 8 documents
2025-07-12 14:56:26,660 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:56:27,749 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:56:28,902 - src.core.embeddings - INFO - Generated embeddings in 2.24 seconds
2025-07-12 14:56:33,125 - src.core.clustering - INFO - Optimal number of clusters: 2 (score: -1.0000)
2025-07-12 14:56:33,128 - src.core.clustering - INFO - Performing gmm clustering with 2 clusters
2025-07-12 14:56:33,301 - src.core.clustering - INFO - Clustering completed successfully with 2 clusters
2025-07-12 14:56:33,302 - src.core.summarization - INFO - Summarizing cluster 0 at level 1 with 7 documents
2025-07-12 14:56:33,303 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:56:34,323 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:57:41,601 - src.core.summarization - INFO - Generated cluster summary in 68.30 seconds
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Created 1 parent nodes at level 1
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Tree construction completed with 2 levels
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 14:57:41,603 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_construction_duration: 74.9432 seconds
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_nodes_count: 9.0000 items
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_levels_count: 2.0000 items
2025-07-12 14:57:41,603 - src.services.raptor_service - INFO - Tree construction completed in 74.94 seconds
2025-07-12 14:57:41,603 - src.services.raptor_service - INFO - Tree statistics: 9 nodes, 2 levels
2025-07-12 14:57:41,604 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:41,604 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 14:57:41,604 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:57:42,665 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_total_duration: 1.1313 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_retrieval_duration: 1.0979 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:42,736 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 14:57:42,736 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_total_duration: 0.0619 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_retrieval_duration: 0.0316 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - nodes_retrieved_count: 3.0000 items
2025-07-12 14:57:42,798 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 14:57:42,799 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 14:57:42,846 - performance.raptor_service - INFO - query_total_duration: 0.0479 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - query_retrieval_duration: 0.0253 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:42,847 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 14:57:42,847 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_total_duration: 0.0770 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_retrieval_duration: 0.0524 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:42,924 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:42,925 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_total_duration: 0.0503 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_retrieval_duration: 0.0257 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:42,975 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 14:57:42,976 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_total_duration: 0.0510 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_retrieval_duration: 0.0273 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - nodes_retrieved_count: 3.0000 items
2025-07-12 14:57:43,027 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 14:57:43,027 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_total_duration: 0.0517 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_retrieval_duration: 0.0260 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:43,079 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 14:57:43,079 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_total_duration: 0.0734 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_retrieval_duration: 0.0509 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:43,153 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:43,154 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_total_duration: 7.9014 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_retrieval_duration: 0.0195 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_generation_duration: 7.8483 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:51,056 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:51,056 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:58:00,469 - performance.raptor_service - INFO - query_total_duration: 9.4134 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - query_retrieval_duration: 0.0208 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - query_generation_duration: 9.3557 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - nodes_retrieved_count: 4.0000 items
2025-07-12 14:58:00,472 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:58:00,472 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_total_duration: 7.8548 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_retrieval_duration: 0.0320 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_generation_duration: 7.7909 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - nodes_retrieved_count: 4.0000 items
2025-07-12 14:58:08,333 - src.services.raptor_service - INFO - Tree environmental_tree saved to ./data/trees/environmental_tree.json
2025-07-12 14:58:08,333 - src.services.raptor_service - INFO - Tree environmental_tree deleted
2025-07-12 14:58:08,351 - src.services.raptor_service - INFO - Tree environmental_tree_loaded loaded from ./data/trees/environmental_tree.json
2025-07-12 14:58:08,352 - src.services.raptor_service - INFO - Querying tree environmental_tree_loaded with strategy tree_traversal
2025-07-12 14:58:08,352 - src.core.retrieval - INFO - Retrieving 2 nodes using tree_traversal strategy
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_total_duration: 0.0504 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_retrieval_duration: 0.0260 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:58:08,403 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:58:08,424 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:58:08,424 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:58:13,477 - src.core.summarization - INFO - Summarization test successful
