2025-07-12 14:46:18,144 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:46:18,145 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:46:19,176 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:46:21,442 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:46:21,442 - src.core.embeddings - INFO - Generating embeddings for 3 documents
2025-07-12 14:46:21,517 - src.core.embeddings - INFO - Generated embeddings in 0.08 seconds
2025-07-12 14:46:25,738 - src.core.clustering - INFO - Optimal number of clusters: 2 (score: -1.0000)
2025-07-12 14:46:25,742 - src.core.clustering - INFO - Performing gmm clustering with 2 clusters
2025-07-12 14:46:25,916 - src.core.clustering - INFO - Clustering completed successfully with 2 clusters
2025-07-12 14:46:25,916 - src.core.clustering - INFO - Clustering validation passed
2025-07-12 14:46:25,916 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:46:25,917 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:46:26,951 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:46:50,323 - src.core.summarization - INFO - Summarization test successful
2025-07-12 14:56:00,375 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 14:56:01,555 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 14:56:01,555 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:56:01,555 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:56:02,596 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:56:03,591 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:56:03,591 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 14:56:03,591 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 14:56:03,591 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:56:03,591 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:56:04,627 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:56:26,658 - src.core.summarization - INFO - Summarization test successful
2025-07-12 14:56:26,659 - src.services.raptor_service - INFO - ✅ Summarization Model test passed
2025-07-12 14:56:26,659 - src.services.raptor_service - INFO - Building RAPTOR tree from 8 documents
2025-07-12 14:56:26,659 - src.core.tree_builder - INFO - Building RAPTOR tree from 8 documents
2025-07-12 14:56:26,660 - src.core.tree_builder - INFO - Building level 1 with 8 nodes
2025-07-12 14:56:26,660 - src.core.embeddings - INFO - Generating embeddings for 8 documents
2025-07-12 14:56:26,660 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:56:27,749 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:56:28,902 - src.core.embeddings - INFO - Generated embeddings in 2.24 seconds
2025-07-12 14:56:33,125 - src.core.clustering - INFO - Optimal number of clusters: 2 (score: -1.0000)
2025-07-12 14:56:33,128 - src.core.clustering - INFO - Performing gmm clustering with 2 clusters
2025-07-12 14:56:33,301 - src.core.clustering - INFO - Clustering completed successfully with 2 clusters
2025-07-12 14:56:33,302 - src.core.summarization - INFO - Summarizing cluster 0 at level 1 with 7 documents
2025-07-12 14:56:33,303 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 14:56:34,323 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 14:57:41,601 - src.core.summarization - INFO - Generated cluster summary in 68.30 seconds
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Created 1 parent nodes at level 1
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Tree construction completed with 2 levels
2025-07-12 14:57:41,602 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 14:57:41,603 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_construction_duration: 74.9432 seconds
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_nodes_count: 9.0000 items
2025-07-12 14:57:41,603 - performance.raptor_service - INFO - tree_levels_count: 2.0000 items
2025-07-12 14:57:41,603 - src.services.raptor_service - INFO - Tree construction completed in 74.94 seconds
2025-07-12 14:57:41,603 - src.services.raptor_service - INFO - Tree statistics: 9 nodes, 2 levels
2025-07-12 14:57:41,604 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:41,604 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 14:57:41,604 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 14:57:42,665 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_total_duration: 1.1313 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_retrieval_duration: 1.0979 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,735 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:42,736 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 14:57:42,736 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_total_duration: 0.0619 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_retrieval_duration: 0.0316 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,798 - performance.raptor_service - INFO - nodes_retrieved_count: 3.0000 items
2025-07-12 14:57:42,798 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 14:57:42,799 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 14:57:42,846 - performance.raptor_service - INFO - query_total_duration: 0.0479 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - query_retrieval_duration: 0.0253 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,847 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:42,847 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 14:57:42,847 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_total_duration: 0.0770 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_retrieval_duration: 0.0524 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,924 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:42,924 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:42,925 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_total_duration: 0.0503 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_retrieval_duration: 0.0257 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:42,975 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:42,975 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 14:57:42,976 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_total_duration: 0.0510 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_retrieval_duration: 0.0273 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,027 - performance.raptor_service - INFO - nodes_retrieved_count: 3.0000 items
2025-07-12 14:57:43,027 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 14:57:43,027 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_total_duration: 0.0517 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_retrieval_duration: 0.0260 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,079 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:43,079 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 14:57:43,079 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_total_duration: 0.0734 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_retrieval_duration: 0.0509 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:57:43,153 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 14:57:43,153 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:43,154 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_total_duration: 7.9014 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_retrieval_duration: 0.0195 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - query_generation_duration: 7.8483 seconds
2025-07-12 14:57:51,055 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:57:51,056 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:57:51,056 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:58:00,469 - performance.raptor_service - INFO - query_total_duration: 9.4134 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - query_retrieval_duration: 0.0208 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - query_generation_duration: 9.3557 seconds
2025-07-12 14:58:00,470 - performance.raptor_service - INFO - nodes_retrieved_count: 4.0000 items
2025-07-12 14:58:00,472 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 14:58:00,472 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_total_duration: 7.8548 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_retrieval_duration: 0.0320 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - query_generation_duration: 7.7909 seconds
2025-07-12 14:58:08,327 - performance.raptor_service - INFO - nodes_retrieved_count: 4.0000 items
2025-07-12 14:58:08,333 - src.services.raptor_service - INFO - Tree environmental_tree saved to ./data/trees/environmental_tree.json
2025-07-12 14:58:08,333 - src.services.raptor_service - INFO - Tree environmental_tree deleted
2025-07-12 14:58:08,351 - src.services.raptor_service - INFO - Tree environmental_tree_loaded loaded from ./data/trees/environmental_tree.json
2025-07-12 14:58:08,352 - src.services.raptor_service - INFO - Querying tree environmental_tree_loaded with strategy tree_traversal
2025-07-12 14:58:08,352 - src.core.retrieval - INFO - Retrieving 2 nodes using tree_traversal strategy
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_total_duration: 0.0504 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_retrieval_duration: 0.0260 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 14:58:08,402 - performance.raptor_service - INFO - nodes_retrieved_count: 2.0000 items
2025-07-12 14:58:08,403 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 14:58:08,424 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 14:58:08,424 - src.core.summarization - INFO - Testing summarization
2025-07-12 14:58:13,477 - src.core.summarization - INFO - Summarization test successful
2025-07-12 16:42:42,130 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:42:43,072 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:42:43,072 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 16:42:43,072 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 16:42:43,073 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:42:43,073 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:42:44,123 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:42:45,135 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:42:45,136 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 16:42:45,136 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 16:42:45,136 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:42:45,136 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 16:42:46,200 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 16:43:07,920 - src.core.summarization - INFO - Summarization test successful
2025-07-12 16:43:07,920 - src.services.raptor_service - INFO - ✅ Summarization Model test passed
2025-07-12 16:43:07,922 - src.services.raptor_service - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:43:07,922 - src.core.tree_builder - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:43:07,922 - src.core.tree_builder - INFO - Tree construction completed with 1 levels
2025-07-12 16:43:07,922 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 16:43:07,922 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 16:43:07,923 - performance.raptor_service - INFO - tree_construction_duration: 0.0007 seconds
2025-07-12 16:43:07,923 - performance.raptor_service - INFO - tree_nodes_count: 1.0000 items
2025-07-12 16:43:07,923 - performance.raptor_service - INFO - tree_levels_count: 1.0000 items
2025-07-12 16:43:07,923 - src.services.raptor_service - INFO - Tree construction completed in 0.00 seconds
2025-07-12 16:43:07,923 - src.services.raptor_service - INFO - Tree statistics: 1 nodes, 1 levels
2025-07-12 16:43:07,924 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:43:07,924 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 16:43:07,924 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:43:09,045 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:43:10,157 - performance.raptor_service - INFO - query_total_duration: 2.2334 seconds
2025-07-12 16:43:10,158 - performance.raptor_service - INFO - query_retrieval_duration: 2.1954 seconds
2025-07-12 16:43:10,158 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,158 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,158 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 16:43:10,158 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 16:43:10,211 - performance.raptor_service - INFO - query_total_duration: 0.0528 seconds
2025-07-12 16:43:10,211 - performance.raptor_service - INFO - query_retrieval_duration: 0.0270 seconds
2025-07-12 16:43:10,211 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,211 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,212 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 16:43:10,212 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 16:43:10,267 - performance.raptor_service - INFO - query_total_duration: 0.0551 seconds
2025-07-12 16:43:10,267 - performance.raptor_service - INFO - query_retrieval_duration: 0.0283 seconds
2025-07-12 16:43:10,267 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,268 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,268 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 16:43:10,268 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 16:43:10,346 - performance.raptor_service - INFO - query_total_duration: 0.0780 seconds
2025-07-12 16:43:10,346 - performance.raptor_service - INFO - query_retrieval_duration: 0.0525 seconds
2025-07-12 16:43:10,346 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,346 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,347 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:43:10,347 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 16:43:10,396 - performance.raptor_service - INFO - query_total_duration: 0.0493 seconds
2025-07-12 16:43:10,396 - performance.raptor_service - INFO - query_retrieval_duration: 0.0241 seconds
2025-07-12 16:43:10,397 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,397 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,398 - src.services.raptor_service - INFO - Tree environmental_tree saved to ./data/trees/environmental_tree.json
2025-07-12 16:43:10,398 - src.services.raptor_service - INFO - Tree environmental_tree deleted
2025-07-12 16:43:10,411 - src.services.raptor_service - INFO - Tree environmental_tree_loaded loaded from ./data/trees/environmental_tree.json
2025-07-12 16:43:10,411 - src.services.raptor_service - INFO - Querying tree environmental_tree_loaded with strategy tree_traversal
2025-07-12 16:43:10,411 - src.core.retrieval - INFO - Retrieving 2 nodes using tree_traversal strategy
2025-07-12 16:43:10,465 - performance.raptor_service - INFO - query_total_duration: 0.0533 seconds
2025-07-12 16:43:10,465 - performance.raptor_service - INFO - query_retrieval_duration: 0.0286 seconds
2025-07-12 16:43:10,465 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:43:10,465 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:43:10,465 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:43:10,492 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:43:10,492 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:43:16,810 - src.core.summarization - INFO - Summarization test successful
2025-07-12 16:44:05,798 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:44:07,337 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:44:07,337 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 16:44:07,337 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 16:44:07,337 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:44:07,337 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:44:08,383 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:44:08,415 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:44:08,415 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 16:44:08,415 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 16:44:08,416 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:44:08,416 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 16:44:09,482 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 16:44:17,142 - src.core.summarization - ERROR - Summarization test failed: invalid summary
2025-07-12 16:44:17,143 - src.services.raptor_service - ERROR - ❌ Summarization Model test failed
2025-07-12 16:44:58,143 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:44:59,018 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:44:59,018 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 16:44:59,018 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 16:44:59,018 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:44:59,018 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:45:00,093 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:45:00,124 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:45:00,124 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 16:45:00,124 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 16:45:00,124 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:45:00,125 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 16:45:01,204 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 16:45:07,406 - src.core.summarization - INFO - Summarization test successful. Summary length: 180, Original length: 216
2025-07-12 16:45:07,406 - src.services.raptor_service - INFO - ✅ Summarization Model test passed
2025-07-12 16:45:07,406 - src.services.raptor_service - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:45:07,406 - src.core.tree_builder - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:45:07,406 - src.core.tree_builder - INFO - Tree construction completed with 1 levels
2025-07-12 16:45:07,407 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 16:45:07,407 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 16:45:07,407 - performance.raptor_service - INFO - tree_construction_duration: 0.0005 seconds
2025-07-12 16:45:07,407 - performance.raptor_service - INFO - tree_nodes_count: 1.0000 items
2025-07-12 16:45:07,407 - performance.raptor_service - INFO - tree_levels_count: 1.0000 items
2025-07-12 16:45:07,407 - src.services.raptor_service - INFO - Tree construction completed in 0.00 seconds
2025-07-12 16:45:07,407 - src.services.raptor_service - INFO - Tree statistics: 1 nodes, 1 levels
2025-07-12 16:45:07,408 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:45:07,408 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 16:45:07,408 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:45:08,487 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:45:08,541 - performance.raptor_service - INFO - query_total_duration: 1.1332 seconds
2025-07-12 16:45:08,541 - performance.raptor_service - INFO - query_retrieval_duration: 1.1087 seconds
2025-07-12 16:45:08,541 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,541 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,542 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 16:45:08,542 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 16:45:08,589 - performance.raptor_service - INFO - query_total_duration: 0.0473 seconds
2025-07-12 16:45:08,589 - performance.raptor_service - INFO - query_retrieval_duration: 0.0236 seconds
2025-07-12 16:45:08,589 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,589 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,589 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 16:45:08,590 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 16:45:08,639 - performance.raptor_service - INFO - query_total_duration: 0.0495 seconds
2025-07-12 16:45:08,639 - performance.raptor_service - INFO - query_retrieval_duration: 0.0255 seconds
2025-07-12 16:45:08,639 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,639 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,640 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 16:45:08,640 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 16:45:08,719 - performance.raptor_service - INFO - query_total_duration: 0.0792 seconds
2025-07-12 16:45:08,719 - performance.raptor_service - INFO - query_retrieval_duration: 0.0471 seconds
2025-07-12 16:45:08,719 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,719 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,720 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:45:08,720 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 16:45:08,768 - performance.raptor_service - INFO - query_total_duration: 0.0478 seconds
2025-07-12 16:45:08,768 - performance.raptor_service - INFO - query_retrieval_duration: 0.0231 seconds
2025-07-12 16:45:08,768 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,768 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,769 - src.services.raptor_service - INFO - Tree environmental_tree saved to ./data/trees/environmental_tree.json
2025-07-12 16:45:08,769 - src.services.raptor_service - INFO - Tree environmental_tree deleted
2025-07-12 16:45:08,780 - src.services.raptor_service - INFO - Tree environmental_tree_loaded loaded from ./data/trees/environmental_tree.json
2025-07-12 16:45:08,780 - src.services.raptor_service - INFO - Querying tree environmental_tree_loaded with strategy tree_traversal
2025-07-12 16:45:08,780 - src.core.retrieval - INFO - Retrieving 2 nodes using tree_traversal strategy
2025-07-12 16:45:08,829 - performance.raptor_service - INFO - query_total_duration: 0.0489 seconds
2025-07-12 16:45:08,829 - performance.raptor_service - INFO - query_retrieval_duration: 0.0237 seconds
2025-07-12 16:45:08,829 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:45:08,830 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:45:08,830 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:45:08,853 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:45:08,854 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:45:16,556 - src.core.summarization - INFO - Summarization test successful. Summary length: 236, Original length: 216
2025-07-12 16:46:21,713 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:46:22,578 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:46:22,578 - src.services.raptor_service - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:46:22,578 - src.core.tree_builder - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:46:22,578 - src.core.tree_builder - INFO - Tree construction completed with 1 levels
2025-07-12 16:46:22,578 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 16:46:22,579 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 16:46:22,579 - performance.raptor_service - INFO - tree_construction_duration: 0.0006 seconds
2025-07-12 16:46:22,579 - performance.raptor_service - INFO - tree_nodes_count: 1.0000 items
2025-07-12 16:46:22,579 - performance.raptor_service - INFO - tree_levels_count: 1.0000 items
2025-07-12 16:46:22,579 - src.services.raptor_service - INFO - Tree construction completed in 0.00 seconds
2025-07-12 16:46:22,579 - src.services.raptor_service - INFO - Tree statistics: 1 nodes, 1 levels
2025-07-12 16:46:22,579 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:46:23,628 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:46:23,670 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:46:23,670 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:46:23,670 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:46:24,712 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:46:24,753 - performance.raptor_service - INFO - query_total_duration: 1.0832 seconds
2025-07-12 16:46:24,753 - performance.raptor_service - INFO - query_retrieval_duration: 1.0629 seconds
2025-07-12 16:46:24,753 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:46:24,753 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:46:24,753 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:46:24,753 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:46:24,795 - performance.raptor_service - INFO - query_total_duration: 0.0415 seconds
2025-07-12 16:46:24,795 - performance.raptor_service - INFO - query_retrieval_duration: 0.0203 seconds
2025-07-12 16:46:24,795 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:46:24,795 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:46:24,795 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:46:24,796 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:46:24,837 - performance.raptor_service - INFO - query_total_duration: 0.0419 seconds
2025-07-12 16:46:24,838 - performance.raptor_service - INFO - query_retrieval_duration: 0.0203 seconds
2025-07-12 16:46:24,838 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:46:24,838 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:46:24,838 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:46:24,838 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:46:24,884 - performance.raptor_service - INFO - query_total_duration: 0.0459 seconds
2025-07-12 16:46:24,884 - performance.raptor_service - INFO - query_retrieval_duration: 0.0262 seconds
2025-07-12 16:46:24,884 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:46:24,884 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:46:24,884 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:46:24,885 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:46:24,926 - performance.raptor_service - INFO - query_total_duration: 0.0413 seconds
2025-07-12 16:46:24,926 - performance.raptor_service - INFO - query_retrieval_duration: 0.0203 seconds
2025-07-12 16:46:24,926 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:46:24,926 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:47:12,329 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:47:13,193 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:47:13,193 - src.services.raptor_service - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:47:13,193 - src.core.tree_builder - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:47:13,194 - src.core.tree_builder - INFO - Generating embeddings for leaf nodes
2025-07-12 16:47:13,194 - src.core.embeddings - INFO - Generating embeddings for 1 documents
2025-07-12 16:47:13,194 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:14,245 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:14,288 - src.core.embeddings - INFO - Generated embeddings in 1.09 seconds
2025-07-12 16:47:14,288 - src.core.tree_builder - INFO - Tree construction completed with 1 levels
2025-07-12 16:47:14,288 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 16:47:14,288 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 16:47:14,288 - performance.raptor_service - INFO - tree_construction_duration: 1.0951 seconds
2025-07-12 16:47:14,289 - performance.raptor_service - INFO - tree_nodes_count: 1.0000 items
2025-07-12 16:47:14,289 - performance.raptor_service - INFO - tree_levels_count: 1.0000 items
2025-07-12 16:47:14,289 - src.services.raptor_service - INFO - Tree construction completed in 1.10 seconds
2025-07-12 16:47:14,289 - src.services.raptor_service - INFO - Tree statistics: 1 nodes, 1 levels
2025-07-12 16:47:14,289 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:15,358 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:15,383 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:47:15,383 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:47:15,383 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:16,446 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:16,492 - performance.raptor_service - INFO - query_total_duration: 1.1083 seconds
2025-07-12 16:47:16,492 - performance.raptor_service - INFO - query_retrieval_duration: 1.0864 seconds
2025-07-12 16:47:16,492 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:16,492 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:16,492 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:47:16,492 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:47:16,537 - performance.raptor_service - INFO - query_total_duration: 0.0448 seconds
2025-07-12 16:47:16,537 - performance.raptor_service - INFO - query_retrieval_duration: 0.0224 seconds
2025-07-12 16:47:16,537 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:16,537 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:16,537 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:47:16,538 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:47:16,579 - performance.raptor_service - INFO - query_total_duration: 0.0416 seconds
2025-07-12 16:47:16,579 - performance.raptor_service - INFO - query_retrieval_duration: 0.0206 seconds
2025-07-12 16:47:16,579 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:16,579 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:16,580 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:47:16,580 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:47:16,625 - performance.raptor_service - INFO - query_total_duration: 0.0458 seconds
2025-07-12 16:47:16,626 - performance.raptor_service - INFO - query_retrieval_duration: 0.0225 seconds
2025-07-12 16:47:16,626 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:16,626 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:16,626 - src.services.raptor_service - INFO - Querying tree test_tree with strategy flat_retrieval
2025-07-12 16:47:16,626 - src.core.retrieval - INFO - Retrieving 5 nodes using flat_retrieval strategy
2025-07-12 16:47:16,667 - performance.raptor_service - INFO - query_total_duration: 0.0415 seconds
2025-07-12 16:47:16,668 - performance.raptor_service - INFO - query_retrieval_duration: 0.0210 seconds
2025-07-12 16:47:16,668 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:16,668 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:48,310 - src.utils.visualization - WARNING - Matplotlib/Seaborn not available. Visualization features disabled.
2025-07-12 16:47:49,200 - src.services.raptor_service - INFO - RAPTOR service initialized successfully
2025-07-12 16:47:49,200 - src.services.raptor_service - INFO - 🧪 Testing RAPTOR system components...
2025-07-12 16:47:49,200 - src.services.raptor_service - INFO - Testing Embedding Model...
2025-07-12 16:47:49,200 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:47:49,200 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:50,256 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:50,306 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:47:50,306 - src.services.raptor_service - INFO - ✅ Embedding Model test passed
2025-07-12 16:47:50,306 - src.services.raptor_service - INFO - Testing Summarization Model...
2025-07-12 16:47:50,306 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:47:50,307 - src.core.summarization - INFO - Initializing LLM: gemma3:12b
2025-07-12 16:47:51,360 - src.core.summarization - INFO - LLM initialized successfully
2025-07-12 16:47:57,336 - src.core.summarization - INFO - Summarization test successful. Summary length: 175, Original length: 216
2025-07-12 16:47:57,336 - src.services.raptor_service - INFO - ✅ Summarization Model test passed
2025-07-12 16:47:57,336 - src.services.raptor_service - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:47:57,337 - src.core.tree_builder - INFO - Building RAPTOR tree from 1 documents
2025-07-12 16:47:57,337 - src.core.tree_builder - INFO - Generating embeddings for leaf nodes
2025-07-12 16:47:57,337 - src.core.embeddings - INFO - Generating embeddings for 1 documents
2025-07-12 16:47:57,337 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:58,383 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:58,516 - src.core.embeddings - INFO - Generated embeddings in 1.18 seconds
2025-07-12 16:47:58,516 - src.core.tree_builder - INFO - Tree construction completed with 1 levels
2025-07-12 16:47:58,517 - src.core.tree_builder - INFO - Optimizing tree structure
2025-07-12 16:47:58,517 - src.core.tree_builder - INFO - Tree optimization completed
2025-07-12 16:47:58,517 - performance.raptor_service - INFO - tree_construction_duration: 1.1801 seconds
2025-07-12 16:47:58,517 - performance.raptor_service - INFO - tree_nodes_count: 1.0000 items
2025-07-12 16:47:58,517 - performance.raptor_service - INFO - tree_levels_count: 1.0000 items
2025-07-12 16:47:58,517 - src.services.raptor_service - INFO - Tree construction completed in 1.18 seconds
2025-07-12 16:47:58,517 - src.services.raptor_service - INFO - Tree statistics: 1 nodes, 1 levels
2025-07-12 16:47:58,518 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:47:58,518 - src.core.retrieval - INFO - Retrieving 3 nodes using tree_traversal strategy
2025-07-12 16:47:58,518 - src.core.embeddings - INFO - Initializing embedding model: nomic-embed-text
2025-07-12 16:47:59,564 - src.core.embeddings - INFO - Embedding model initialized successfully
2025-07-12 16:47:59,613 - performance.raptor_service - INFO - query_total_duration: 1.0951 seconds
2025-07-12 16:47:59,613 - performance.raptor_service - INFO - query_retrieval_duration: 1.0708 seconds
2025-07-12 16:47:59,613 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:59,613 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:59,614 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy flat_retrieval
2025-07-12 16:47:59,614 - src.core.retrieval - INFO - Retrieving 3 nodes using flat_retrieval strategy
2025-07-12 16:47:59,661 - performance.raptor_service - INFO - query_total_duration: 0.0475 seconds
2025-07-12 16:47:59,661 - performance.raptor_service - INFO - query_retrieval_duration: 0.0239 seconds
2025-07-12 16:47:59,661 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:59,661 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:59,662 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy level_based
2025-07-12 16:47:59,662 - src.core.retrieval - INFO - Retrieving 3 nodes using level_based strategy
2025-07-12 16:47:59,711 - performance.raptor_service - INFO - query_total_duration: 0.0491 seconds
2025-07-12 16:47:59,711 - performance.raptor_service - INFO - query_retrieval_duration: 0.0243 seconds
2025-07-12 16:47:59,711 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:59,711 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:59,711 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy hybrid
2025-07-12 16:47:59,711 - src.core.retrieval - INFO - Retrieving 3 nodes using hybrid strategy
2025-07-12 16:47:59,788 - performance.raptor_service - INFO - query_total_duration: 0.0767 seconds
2025-07-12 16:47:59,788 - performance.raptor_service - INFO - query_retrieval_duration: 0.0516 seconds
2025-07-12 16:47:59,788 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:47:59,789 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:47:59,789 - src.services.raptor_service - INFO - Querying tree environmental_tree with strategy tree_traversal
2025-07-12 16:47:59,789 - src.core.retrieval - INFO - Retrieving 4 nodes using tree_traversal strategy
2025-07-12 16:48:51,707 - performance.raptor_service - INFO - query_total_duration: 51.9178 seconds
2025-07-12 16:48:51,707 - performance.raptor_service - INFO - query_retrieval_duration: 0.0259 seconds
2025-07-12 16:48:51,707 - performance.raptor_service - INFO - query_generation_duration: 51.8631 seconds
2025-07-12 16:48:51,707 - performance.raptor_service - INFO - nodes_retrieved_count: 1.0000 items
2025-07-12 16:48:51,709 - src.services.raptor_service - INFO - Tree environmental_tree saved to ./data/trees/environmental_tree.json
2025-07-12 16:48:51,709 - src.services.raptor_service - INFO - Tree environmental_tree deleted
2025-07-12 16:48:51,718 - src.services.raptor_service - INFO - Tree environmental_tree_loaded loaded from ./data/trees/environmental_tree.json
2025-07-12 16:48:51,718 - src.services.raptor_service - INFO - Querying tree environmental_tree_loaded with strategy tree_traversal
2025-07-12 16:48:51,719 - src.core.retrieval - INFO - Retrieving 2 nodes using tree_traversal strategy
2025-07-12 16:48:51,768 - performance.raptor_service - INFO - query_total_duration: 0.0492 seconds
2025-07-12 16:48:51,768 - performance.raptor_service - INFO - query_retrieval_duration: 0.0266 seconds
2025-07-12 16:48:51,768 - performance.raptor_service - INFO - query_generation_duration: 0.0000 seconds
2025-07-12 16:48:51,768 - performance.raptor_service - INFO - nodes_retrieved_count: 0.0000 items
2025-07-12 16:48:51,768 - src.core.embeddings - INFO - Testing embedding model
2025-07-12 16:48:51,795 - src.core.embeddings - INFO - Embedding model test successful. Dimension: 768
2025-07-12 16:48:51,795 - src.core.summarization - INFO - Testing summarization
2025-07-12 16:48:59,742 - src.core.summarization - INFO - Summarization test successful. Summary length: 181, Original length: 216
