# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-embed-text
CHAT_MODEL=gemma3:12b

# Model Parameters
TEMPERATURE=0.1
MAX_TOKENS=1000
TOP_P=0.9

# Document Processing
CHUNK_SIZE=500
CHUNK_OVERLAP=50
MAX_DOCUMENTS=100

# Retrieval Settings
RETRIEVAL_K=4
SIMILARITY_THRESHOLD=0.7
SEARCH_TYPE=similarity

# Vector Store Configuration
VECTORSTORE_TYPE=chroma
VECTORSTORE_PATH=./data/vectorstore
COLLECTION_NAME=rag_documents

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/rag_agent.log
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Performance Settings
ENABLE_CACHING=true
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=10

# Quality Control
ENABLE_RELEVANCE_CHECK=true
ENABLE_HALLUCINATION_CHECK=true
ENABLE_DOCUMENT_HIGHLIGHT=true
MIN_RELEVANCE_SCORE=0.6

# Data Sources
DEFAULT_URLS=https://www.deeplearning.ai/the-batch/how-agents-can-improve-llm-performance/?ref=dl-staging-website.ghost.io,https://www.deeplearning.ai/the-batch/agentic-design-patterns-part-2-reflection/?ref=dl-staging-website.ghost.io

# API Configuration (if using API mode)
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Security (if needed)
# API_KEY=your_api_key_here
# ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Development Settings
DEBUG=false
ENABLE_PROFILING=false
SAVE_INTERMEDIATE_RESULTS=false
