import os
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain.agents import AgentType
from langchain_deepseek import ChatDeepSeek
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Load environment variables
from dotenv import load_dotenv

# Load environment variables and set DeepSeek API key
load_dotenv()
os.environ["DEEPSEEK_API_KEY"] = os.getenv('DEEPSEEK_API_KEY')

# Create a simple test dataset
np.random.seed(42)
data = {
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35, 28, 32],
    'Salary': [50000, 60000, 70000, 55000, 65000],
    'Department': ['IT', 'HR', 'Finance', 'IT', 'Marketing']
}

df = pd.DataFrame(data)
print("Test dataset:")
print(df)

# Create the Pandas DataFrame agent with DeepSeek
try:
    agent = create_pandas_dataframe_agent(
        ChatDeepSeek(model="deepseek-chat", temperature=0),
        df,
        verbose=True,
        allow_dangerous_code=True,
        agent_type=AgentType.OPENAI_FUNCTIONS,
    )
    print("\n✅ DeepSeek agent created successfully!")
    
    # Test a simple query
    print("\n🔍 Testing with a simple query...")
    result = agent.invoke("What is the average salary?")
    print(f"Result: {result}")
    
except Exception as e:
    print(f"❌ Error creating DeepSeek agent: {e}")
