"""Embedding management functionality."""

from typing import List, Optional, Dict, Any
import logging
import time

from langchain_ollama import OllamaEmbeddings
from langchain_core.documents import Document

from config.settings import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmbeddingManager:
    """Manages document embeddings using Ollama."""
    
    def __init__(self):
        """Initialize the embedding manager."""
        self.settings = get_settings()
        self._embedding_model = None
        
    @property
    def embedding_model(self) -> OllamaEmbeddings:
        """Get or create the embedding model."""
        if self._embedding_model is None:
            logger.info(f"Initializing embedding model: {self.settings.embedding_model}")
            try:
                self._embedding_model = OllamaEmbeddings(
                    model=self.settings.embedding_model,
                    base_url=self.settings.ollama_base_url
                )
                logger.info("Embedding model initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize embedding model: {e}")
                raise
        return self._embedding_model
    
    def embed_documents(self, documents: List[Document]) -> List[List[float]]:
        """Generate embeddings for a list of documents.
        
        Args:
            documents: List of documents to embed
            
        Returns:
            List of embedding vectors
        """
        logger.info(f"Generating embeddings for {len(documents)} documents")
        start_time = time.time()
        
        try:
            texts = [doc.page_content for doc in documents]
            embeddings = self.embedding_model.embed_documents(texts)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Generated embeddings in {elapsed_time:.2f} seconds")
            
            return embeddings
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def embed_query(self, query: str) -> List[float]:
        """Generate embedding for a query string.
        
        Args:
            query: Query string to embed
            
        Returns:
            Embedding vector
        """
        logger.debug(f"Generating embedding for query: {query[:100]}...")
        
        try:
            embedding = self.embedding_model.embed_query(query)
            logger.debug("Query embedding generated successfully")
            return embedding
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise
    
    def test_embedding_model(self) -> bool:
        """Test if the embedding model is working correctly.
        
        Returns:
            True if the model is working, False otherwise
        """
        logger.info("Testing embedding model")
        
        try:
            test_text = "This is a test sentence for embedding."
            embedding = self.embed_query(test_text)
            
            if embedding and len(embedding) > 0:
                logger.info(f"Embedding model test successful. Dimension: {len(embedding)}")
                return True
            else:
                logger.error("Embedding model test failed: empty embedding")
                return False
                
        except Exception as e:
            logger.error(f"Embedding model test failed: {e}")
            return False
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of the embedding vectors.
        
        Returns:
            Embedding dimension
        """
        try:
            test_embedding = self.embed_query("test")
            return len(test_embedding)
        except Exception as e:
            logger.error(f"Failed to get embedding dimension: {e}")
            return 0
    
    def batch_embed_documents(self, documents: List[Document], 
                            batch_size: int = 100) -> List[List[float]]:
        """Generate embeddings for documents in batches.
        
        Args:
            documents: List of documents to embed
            batch_size: Size of each batch
            
        Returns:
            List of embedding vectors
        """
        logger.info(f"Generating embeddings for {len(documents)} documents in batches of {batch_size}")
        
        all_embeddings = []
        total_batches = (len(documents) + batch_size - 1) // batch_size
        
        for i in range(0, len(documents), batch_size):
            batch_num = i // batch_size + 1
            batch_docs = documents[i:i + batch_size]
            
            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch_docs)} documents)")
            
            try:
                batch_embeddings = self.embed_documents(batch_docs)
                all_embeddings.extend(batch_embeddings)
                
                # Add a small delay between batches to avoid overwhelming the model
                if batch_num < total_batches:
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Failed to process batch {batch_num}: {e}")
                # Add empty embeddings for failed batch
                all_embeddings.extend([[] for _ in batch_docs])
        
        logger.info(f"Completed embedding generation for all documents")
        return all_embeddings
    
    def calculate_similarity(self, embedding1: List[float], 
                           embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score
        """
        try:
            import numpy as np
            
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Failed to calculate similarity: {e}")
            return 0.0
