# RAG Agent System

一个基于 Ollama 的检索增强生成 (RAG) 系统，用于智能文档问答。

## 🚀 功能特性

- **文档检索**: 从多个网页源加载和索引文档
- **相关性评估**: 智能过滤不相关的检索结果
- **答案生成**: 基于检索到的文档生成准确答案
- **幻觉检测**: 验证生成答案的事实准确性
- **文档高亮**: 标识用于生成答案的具体文档片段
- **本地化部署**: 使用 Ollama 实现完全本地化运行

## 📁 项目结构

```
rag-agent/
├── config/                 # 配置文件
│   ├── __init__.py
│   ├── settings.py         # 主配置文件
│   └── prompts.py          # 提示词模板
├── src/                    # 源代码
│   ├── __init__.py
│   ├── core/               # 核心功能
│   │   ├── __init__.py
│   │   ├── document_loader.py
│   │   ├── embeddings.py
│   │   ├── retriever.py
│   │   └── llm.py
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── schemas.py
│   │   └── validators.py
│   ├── services/           # 业务逻辑
│   │   ├── __init__.py
│   │   ├── relevance_service.py
│   │   ├── generation_service.py
│   │   ├── hallucination_service.py
│   │   └── highlight_service.py
│   └── utils/              # 工具函数
│       ├── __init__.py
│       ├── logger.py
│       └── helpers.py
├── data/                   # 数据目录
│   ├── documents/          # 文档存储
│   └── vectorstore/        # 向量数据库
├── tests/                  # 测试文件
│   ├── __init__.py
│   ├── test_core/
│   ├── test_services/
│   └── test_integration/
├── scripts/                # 脚本文件
│   ├── setup.py           # 环境设置
│   └── run_pipeline.py    # 运行完整流程
├── requirements.txt        # 依赖包
├── .env.example           # 环境变量示例
├── .gitignore             # Git 忽略文件
├── docker-compose.yml     # Docker 配置
└── README.md              # 项目说明
```

## 🛠️ 安装和设置

### 1. 环境要求

- Python 3.8+
- Ollama
- Git

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置 Ollama

```bash
# 安装所需模型
ollama pull nomic-embed-text
ollama pull gemma3:12b

# 启动 Ollama 服务
ollama serve
```

### 4. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置文件
nano .env
```

## 🚀 快速开始

### 基本使用

```python
from src.services.generation_service import RAGPipeline

# 初始化 RAG 系统
rag = RAGPipeline()

# 提问
question = "What are the different agentic design patterns?"
answer = rag.ask(question)
print(answer)
```

### 运行完整流程

```bash
python scripts/run_pipeline.py
```

## 📊 系统组件

### 1. 文档加载器 (Document Loader)
- 支持多种文档源 (Web, PDF, TXT)
- 智能文档分块
- 元数据提取

### 2. 嵌入系统 (Embeddings)
- 使用 Ollama nomic-embed-text 模型
- 高质量向量表示
- 本地化处理

### 3. 检索器 (Retriever)
- 基于相似度的文档检索
- 可配置的检索参数
- 相关性过滤

### 4. 生成器 (Generator)
- 基于检索文档的答案生成
- 上下文感知
- 可控制的输出长度

### 5. 质量控制
- 相关性评估
- 幻觉检测
- 文档高亮

## 🔧 配置选项

主要配置在 `config/settings.py` 中：

```python
# Ollama 配置
OLLAMA_BASE_URL = "http://localhost:11434"
EMBEDDING_MODEL = "nomic-embed-text"
CHAT_MODEL = "gemma3:12b"

# 检索配置
CHUNK_SIZE = 500
CHUNK_OVERLAP = 50
RETRIEVAL_K = 4

# 生成配置
TEMPERATURE = 0.1
MAX_TOKENS = 1000
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_services/test_generation_service.py
```

## 📈 性能监控

系统包含内置的性能监控和日志记录：

- 检索延迟监控
- 生成质量评估
- 错误追踪
- 使用统计

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系维护者。
